<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>通讯录</title>
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="style/contract.css" />
		<script src="tools/jquery-3.7.1.min.js"></script>
	</head>

	<body>
		<div class="contract-container">
			<div class="sidebar">
				<div class="sidebar-header">
					<h3>通讯录</h3>
				</div>
				<div class="sidebar-menu">
					<div class="menu-item active" data-type="internal">
						<i class="layui-icon layui-icon-username"></i>
						<span>内部通讯录</span>
					</div>
					<div class="menu-item" data-type="frequent">
						<i class="layui-icon layui-icon-star"></i>
						<span>常用联系人</span>
					</div>
					<div class="menu-item" data-type="mygroup">
						<i class="layui-icon layui-icon-group"></i>
						<span>我的分组</span>
					</div>
				</div>
			</div>

			<div class="department-panel">
				<div class="department-list" id="departmentList"></div>
			</div>

			<div class="personnel-panel">
				<div class="panel-header">
					<div class="breadcrumb" id="breadcrumb">湖南省财政厅</div>
					<span class="personnel-count" id="personnelCount"></span>
				</div>
				<div class="personnel-list" id="personnelList"></div>
			</div>

			<!-- 常用联系人内容区域 -->
			<div class="frequent-content" id="frequentContent" style="display: none">
				<div class="frequent-header">
					<h2>常用联系人</h2>
				</div>
				<div class="frequent-body" id="frequentBody"></div>
			</div>

			<!-- 我的分组内容区域 -->
			<div class="mygroup-content" id="mygroupContent" style="display: none">
				<div class="mygroup-overview" id="mygroupOverview">
					<div class="mygroup-header">
						<h2>我的分组</h2>
					</div>
					<div class="mygroup-body" id="mygroupBody"></div>
				</div>

				<!-- 分组详情页面 -->
				<div class="mygroup-detail" id="mygroupDetail" style="display: none">
					<div class="mygroup-detail-header">
						<div class="breadcrumb-nav">
							<span class="nav-item" id="backToGroups">我的分组</span>
							<span class="nav-separator">></span>
							<span class="nav-item current" id="currentGroupName"></span>
							<span class="member-count" id="groupMemberCount"></span>
						</div>
						<div class="group-chat-btn">
							<button
								id="btnChat"
								class="layui-btn layui-btn-primary layui-bg-blue layui-btn-radius"
							>
								<i class="layui-icon layui-icon-add-1"></i>
								发起群聊
							</button>
						</div>
					</div>
					<div class="mygroup-detail-body" id="mygroupDetailBody"></div>
				</div>
			</div>
		</div>

		<script src="layui/layui.js"></script>
		<script>
			// 通讯录数据
			const contractData = {
				internal: {
					title: '湖南省财政厅',
					departments: [
						{
							id: 1,
							name: '厅领导',
							personnel: [
								{
									name: '张三',
									position: '厅长',
									phone: '13800138001',
									email: '<EMAIL>',
								},
								{
									name: '李四',
									position: '副厅长',
									phone: '13800138002',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 2,
							name: '办公室',
							personnel: [
								{
									name: '王五',
									position: '主任',
									phone: '13800138003',
									email: '<EMAIL>',
								},

								{
									name: '赵六',
									position: '副主任',
									phone: '13800138004',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 3,
							name: '政研室',
							personnel: [
								{
									name: '钱七',
									position: '主任',
									phone: '13800138005',
									email: '<EMAIL>',
								},

								{
									name: '孙八',
									position: '副主任',
									phone: '13800138006',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 4,
							name: '综合处',
							personnel: [
								{
									name: '周九',
									position: '处长',
									phone: '13800138007',
									email: '<EMAIL>',
								},

								{
									name: '吴十',
									position: '副处长',
									phone: '13800138008',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 5,
							name: '税政法规处',
							personnel: [
								{
									name: '郑一',
									position: '处长',
									phone: '13800138009',
									email: '<EMAIL>',
								},

								{
									name: '王二',
									position: '副处长',
									phone: '13800138010',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 6,
							name: '预算处',
							personnel: [
								{
									name: '李三',
									position: '处长',
									phone: '13800138011',
									email: '<EMAIL>',
								},

								{
									name: '张四',
									position: '副处长',
									phone: '13800138012',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 7,
							name: '国库处',
							personnel: [
								{
									name: '赵五',
									position: '处长',
									phone: '13800138013',
									email: '<EMAIL>',
								},

								{
									name: '钱六',
									position: '副处长',
									phone: '13800138014',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 8,
							name: '市县财政处',
							personnel: [
								{
									name: '孙七',
									position: '处长',
									phone: '13800138015',
									email: '<EMAIL>',
								},

								{
									name: '周八',
									position: '副处长',
									phone: '13800138016',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 9,
							name: '行政处',
							personnel: [
								{
									name: '吴九',
									position: '处长',
									phone: '13800138017',
									email: '<EMAIL>',
								},

								{
									name: '郑十',
									position: '副处长',
									phone: '13800138018',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 10,
							name: '政法处',
							personnel: [
								{
									name: '王一一',
									position: '处长',
									phone: '13800138019',
									email: '<EMAIL>',
								},

								{
									name: '李一二',
									position: '副处长',
									phone: '13800138020',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 11,
							name: '科教处',
							personnel: [
								{
									name: '张一三',
									position: '处长',
									phone: '13800138021',
									email: '<EMAIL>',
								},

								{
									name: '赵一四',
									position: '副处长',
									phone: '13800138022',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 12,
							name: '文化处',
							personnel: [
								{
									name: '钱一五',
									position: '处长',
									phone: '13800138023',
									email: '<EMAIL>',
								},

								{
									name: '孙一六',
									position: '副处长',
									phone: '13800138024',
									email: '<EMAIL>',
								},
							],
						},

						{
							id: 13,
							name: '经济建设处',
							personnel: [
								{
									name: '周一七',
									position: '处长',
									phone: '13800138025',
									email: '<EMAIL>',
								},

								{
									name: '吴一八',
									position: '副处长',
									phone: '13800138026',
									email: '<EMAIL>',
								},
							],
						},
					],
				},
				frequent: {
					title: '常用联系人',
					contacts: [
						{
							id: 1,
							name: '陈博彰',
							position: '党组书记、厅长',
							department: '厅领导',
							phone: '18025561234',
							email: '<EMAIL>',
							category: 'C',
						},

						{
							id: 2,
							name: '曹云辉',
							position: '会馆中心主任（驻京）',
							department: '办公室',
							phone: '13800138002',
							email: '<EMAIL>',
							category: 'C',
						},

						{
							id: 3,
							name: '黄斌',
							position: '县域融资中心主任、办公室（政研室）党支部副书记',
							department: '政研室',
							phone: '13800138003',
							email: '<EMAIL>',
							category: 'H',
						},

						{
							id: 4,
							name: '黄卓琳',
							position: '二级主任科员',
							department: '预算处',
							phone: '13800138004',
							email: '<EMAIL>',
							category: 'H',
						},
					],
					emptyText: '未设置常用联系人',
				},
				mygroup: {
					title: '我的分组',
					groups: [
						{
							id: 1,
							name: '智慧办公项目组',
							icon: 'layui-icon-group',
							members: [
								{
									id: 1,
									name: '张三',
									position: '项目经理',
									department: '信息中心',
									phone: '13800138001',
									email: '<EMAIL>',
									category: 'Z',
								},

								{
									id: 2,
									name: '李四',
									position: '技术负责人',
									department: '信息中心',
									phone: '13800138002',
									email: '<EMAIL>',
									category: 'L',
								},
							],
						},

						{
							id: 2,
							name: '邮件发送组',
							icon: 'layui-icon-group',
							members: [
								{
									id: 3,
									name: '陈博彰',
									position: '党组书记、厅长',
									department: '厅领导',
									phone: '18025561234',
									email: '<EMAIL>',
									category: 'C',
								},

								{
									id: 4,
									name: '曹云辉',
									position: '会馆中心主任（驻京）',
									department: '办公室',
									phone: '13800138002',
									email: '<EMAIL>',
									category: 'C',
								},

								{
									id: 5,
									name: '黄斌',
									position: '县域融资中心主任、办公室（政研室）党支部副书记',
									department: '政研室',
									phone: '13800138003',
									email: '<EMAIL>',
									category: 'H',
								},

								{
									id: 6,
									name: '黄卓琳',
									position: '二级主任科员',
									department: '预算处',
									phone: '13800138004',
									email: '<EMAIL>',
									category: 'H',
								},

								{
									id: 7,
									name: '贺倩',
									position: '副处长',
									department: '绩效管理处',
									phone: '13800138005',
									email: '<EMAIL>',
									category: 'H',
								},
							],
						},
					],
					emptyText: '暂无分组',
				},
			};

			let currentType = 'internal';
			let selectedDepartment = null;
			let selectedGroup = null;

			/**
			 * 初始化页面
			 */
			function initPage() {
				// 默认加载内部通讯录并显示所有部门
				loadInternalContacts();
				bindEvents();
			}

			/**
			 * 绑定事件
			 */
			function bindEvents() {
				$('.menu-item').on('click', function () {
					const type = $(this).data('type');

					if (type !== currentType) {
						$('.menu-item').removeClass('active');
						$(this).addClass('active');
						currentType = type;
						selectedDepartment = null;

						// 根据不同类型加载不同内容
						switch (type) {
							case 'internal':
								loadInternalContacts();
								break;
							case 'frequent':
								loadFrequentContacts();
								break;
							case 'mygroup':
								loadMyGroups();
								break;
						}
					}
				});
				$('#btnChat').on('click', function () {
					const layer = layui.layer;
					layer.msg('跳转到聊天页面...');
				});
			}

			/**
			 * 加载内部通讯录
			 */
			function loadInternalContacts() {
				// 显示分栏布局，隐藏其他布局
				$('.department-panel').show();
				$('.personnel-panel').show();
				$('#frequentContent').hide();
				$('#mygroupContent').hide();

				const data = contractData.internal;
				const $departmentList = $('#departmentList');

				// 构建部门列表HTML
				let html = `<div class="department-header"data-id="0"><span>${
					data.title
					/*  */
				}

  </span></div>`;

				data.departments.forEach((dept) => {
					html += `<div class="department-item"data-id="${dept.id}"><span>${dept.name}

      </span></div>`;
				});

				$departmentList.html(html);

				// 默认选中总入口并显示所有部门
				$('.department-header').addClass('active');
				loadAllDepartments(data);

				// 绑定总入口点击事件
				$('.department-header').on('click', function () {
					$('.department-item, .department-header').removeClass('active');
					$(this).addClass('active');
					selectedDepartment = null;
					loadAllDepartments(data);
				});

				// 绑定部门点击事件
				$('.department-item').on('click', function () {
					const deptId = parseInt($(this).data('id'));
					const department = data.departments.find((d) => d.id === deptId);

					$('.department-item, .department-header').removeClass('active');
					$(this).addClass('active');

					selectedDepartment = department;
					loadPersonnel(department);
				});
			}

			function loadAllDepartments(data) {
				const $personnelList = $('#personnelList');
				updateBreadcrumb(data.title);
				updatePersonnelCount(data.departments.length, '部门');
				let html = '';

				data.departments.forEach((dept) => {
					html += `<div class="personnel-item department-card"data-id="${dept.id}"><div class="personnel-avatar blue"><i class="layui-icon layui-icon-home"></i></div><div class="personnel-info"><div class="personnel-name">${dept.name}

      </div></div></div>`;
				});
				$personnelList.html(html);

				$('.department-card').on('click', function () {
					const deptId = parseInt($(this).data('id'));
					const department = data.departments.find((d) => d.id === deptId);
					$('.department-item').removeClass('active');
					$(`.department-item[data-id="${deptId}"]`).addClass('active');
					selectedDepartment = department;
					loadPersonnel(department);
				});
			}

			/**
			 * 加载常用联系人（预留接口）
			 */
			/**
			 * 加载常用联系人
			 */
			function loadFrequentContacts() {
				// 隐藏其他布局，显示常用联系人布局
				$('.department-panel').hide();
				$('.personnel-panel').hide();
				$('#mygroupContent').hide();
				$('#frequentContent').show();

				const data = contractData.frequent;
				const $frequentBody = $('#frequentBody');

				if (!data.contacts || data.contacts.length === 0) {
					$frequentBody.html(` <div class="frequent-empty-state"> <div class="empty-icon"> <i class="layui-icon layui-icon-star"></i> </div> <div class="empty-text">${data.emptyText}

      </div> </div> `);
					return;
				}

				// 按首字母分组
				const groupedContacts = groupContactsByCategory(data.contacts);
				let html = '';

				Object.keys(groupedContacts)
					.sort()
					.forEach((category) => {
						html += `<div class="frequent-category"> <div class="category-header">${category}

      </div> <div class="category-contacts">`;

						groupedContacts[category].forEach((contact) => {
							const nameLength = contact.name.length;
							const avatarText =
								nameLength >= 2 ? contact.name.substring(nameLength - 2) : contact.name;

							html += ` <div class="frequent-contact-item"data-contact='${JSON.stringify(
								contact
							)}'> <div class="contact-avatar"> <span class="avatar-text">${avatarText}

          </span></div> <div class="contact-info"> <div class="contact-top"> <div class="contact-name">${
						contact.name
					}

          </div> <div class="contact-position">${contact.position}

          </div> </div> <div class="contact-department">${contact.department}

          </div> </div> </div> `;
						});
						html += `</div></div>`;
					});

				$frequentBody.html(html);

				// 绑定点击事件
				$('.frequent-contact-item').on('click', function () {
					const contactData = JSON.parse($(this).attr('data-contact'));
					showPersonnelDetail(contactData, contactData.department);
				});
			}

			/**
			 * 按分类分组联系人
			 */
			function groupContactsByCategory(contacts) {
				const grouped = {};

				contacts.forEach((contact) => {
					const category = contact.category;

					if (!grouped[category]) {
						grouped[category] = [];
					}

					grouped[category].push(contact);
				});
				return grouped;
			}

			/**
			 * 加载我的分组
			 */
			function loadMyGroups() {
				// 隐藏其他布局，显示我的分组布局
				$('.department-panel').hide();
				$('.personnel-panel').hide();
				$('#frequentContent').hide();
				$('#mygroupContent').show();

				// 重置状态，显示概览页面
				selectedGroup = null;
				$('#mygroupOverview').show();
				$('#mygroupDetail').hide();

				const data = contractData.mygroup;
				const $mygroupBody = $('#mygroupBody');

				if (!data.groups || data.groups.length === 0) {
					$mygroupBody.html(` <div class="mygroup-empty-state"> <div class="empty-icon"> <i class="layui-icon layui-icon-friends"></i> </div> <div class="empty-text">${data.emptyText}

      </div> </div> `);
					return;
				}

				// 构建分组列表HTML
				let html = '';
				data.groups.forEach((group) => {
					html += ` <div class="group-item"data-group-id="${group.id}"> <div class="group-avatar"> <i class="layui-icon ${group.icon}"></i> </div> <div class="group-info"> <div class="group-name">${group.name}

      </div> </div> </div> `;
				});

				$mygroupBody.html(html);

				// 绑定分组点击事件
				$('.group-item').on('click', function () {
					const groupId = parseInt($(this).data('group-id'));
					const group = data.groups.find((g) => g.id === groupId);

					if (group) {
						showGroupDetail(group);
					}
				});
			}

			/**
			 * 显示分组详情
			 */
			function showGroupDetail(group) {
				selectedGroup = group;

				// 切换到详情页面
				$('#mygroupOverview').hide();
				$('#mygroupDetail').show();

				// 更新面包屑导航
				$('#currentGroupName').text(group.name);

				$('#groupMemberCount').text(` ( ${group.members.length}

      )`);

				const $detailBody = $('#mygroupDetailBody');

				// 构建成员列表HTML
				let html = '';

				group.members.forEach((member) => {
					const nameLength = member.name.length;
					const avatarText = nameLength >= 2 ? member.name.substring(nameLength - 2) : member.name;

					html += ` <div class="group-member-item"data-member='${JSON.stringify(
						member
					)}'> <div class="member-avatar"> <span class="avatar-text">${avatarText}

      </span> </div> <div class="member-info"> <div class="member-top"> <div class="member-name">${
				member.name
			}

      </div> <div class="member-position">${member.position}

      </div> </div> <div class="member-department">${member.department}

      </div> </div> </div> `;
				});

				$detailBody.html(html);

				// 绑定成员点击事件
				$('.group-member-item').on('click', function () {
					const memberData = JSON.parse($(this).attr('data-member'));
					showPersonnelDetail(memberData, memberData.department);
				});

				// 绑定返回按钮事件
				$('#backToGroups')
					.off('click')
					.on('click', function () {
						loadMyGroups();
					});
			}

			function loadPersonnel(department) {
				const $personnelList = $('#personnelList');

				updateBreadcrumb(`湖南省财政厅 > ${department.name}

    `);
				updatePersonnelCount(department.personnel.length, '人员');
				let html = '';

				department.personnel.forEach((person) => {
					const nameLength = person.name.length;
					const avatarText = nameLength >= 2 ? person.name.substring(nameLength - 2) : person.name;

					html += `<div class="personnel-item"data-person='${JSON.stringify(person)}

              'data-department="${department.name}

              "><div class="personnel-avatar blue"><span class="avatar-text">${avatarText}


              </span></div><div class="personnel-info"><div class="personnel-name">${person.name}

              </div><div class="personnel-position">${person.position}

              </div></div></div>`;
				});
				$personnelList.html(html);

				$('.personnel-item').on('click', function () {
					const personData = JSON.parse($(this).attr('data-person'));
					const departmentName = $(this).attr('data-department');
					showPersonnelDetail(personData, departmentName);
				});
			}

			function updateBreadcrumb(text) {
				$('#breadcrumb').text(text);
			}

			function updatePersonnelCount(count, type) {
				$('#personnelCount').text(`${type}（ ${count} ）`);
			}
			// 显示联系人详情弹窗
			function showPersonnelDetail(person, departmentName) {
				const nameLength = person.name.length;
				const avatarText = nameLength >= 2 ? person.name.substring(nameLength - 2) : person.name;

				layui.use(['layer'], function () {
					const layer = layui.layer;

					const content = ` <div class="personnel-detail"> <div class="detail-header"> <div class="detail-avatar blue"> <span class="avatar-text">${avatarText}

            </span> </div> <div class="detail-name">${person.name}

            </div> <div class="detail-favorite"> <i class="layui-icon layui-icon-star"></i> </div> </div> <div class="detail-info"> <div class="info-item"> <span class="info-label">部门：</span> <span class="info-value">${departmentName}

            </span> </div> <div class="info-item"> <span class="info-label">职务：</span> <span class="info-value">${person.position}

            </span> </div> <div class="info-item"> <span class="info-label">手机：</span> <span class="info-value">${person.phone}

            </span> </div> <div class="info-item"> <span class="info-label">邮箱：</span> <span class="info-value">${person.email}

            <i class="layui-icon layui-icon-share"style="margin-left:8px;"></i></span> </div> </div> <div class="detail-actions"> <button class="layui-btn layui-btn-primary layui-btn-fluid">发消息</button> </div> </div> `;

					layer.open({
						type: 1,
						title: false,
						closeBtn: 1,
						area: ['400px', 'auto'],
						skin: 'personnel-detail-layer',
						shade: 0.02,
						shadeClose: true,
						content: content,
						success: function (layero, index) {},
					});
				});
			}

			$(document).ready(function () {
				initPage();
			});
		</script>
	</body>
</html>
