<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>忘记密码</title>
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="style/forget_password.css" />
		<script src="tools/jquery-3.7.1.min.js"></script>
	</head>

	<body>
		<div class="forget-password-container">
			<!-- 返回登录按钮 -->
			<a href="login.html" class="back-to-login">
				<i class="layui-icon layui-icon-left"></i> 返回登录
			</a>

			<div class="step-indicator">
				<div class="step-item" data-step="1">
					<div class="step-circle active">1</div>
					<div class="step-label">确认账号</div>
				</div>
				<div class="step-line"></div>
				<div class="step-item" data-step="2">
					<div class="step-circle">2</div>
					<div class="step-label">验证身份</div>
				</div>
				<div class="step-line"></div>
				<div class="step-item" data-step="3">
					<div class="step-circle">3</div>
					<div class="step-label">设置新密码</div>
				</div>
			</div>
			<div class="step-content">
				<!-- 步骤1：确认账号 -->
				<div class="step-pane active" id="step1">
					<form class="layui-form" lay-filter="accountForm">
						<div class="layui-form-item">
							<div class="layui-input-wrap">
								<div class="layui-input-prefix">
									<i class="layui-icon layui-icon-username"></i>
								</div>
								<input
									type="text"
									name="username"
									placeholder="请输入用户名"
									class="layui-input"
									lay-verify="required"
								/>
							</div>
						</div>
						<button class="layui-btn btn-next" lay-submit lay-filter="step1Submit">下一步</button>
					</form>
				</div>

				<!-- 步骤2：验证身份 -->
				<div class="step-pane" id="step2">
					<div class="phone-display">
						<i class="layui-icon layui-icon-cellphone"></i>
						<span id="phoneNumber">176*****691</span>
					</div>
					<form class="layui-form" lay-filter="verifyForm">
						<div class="layui-form-item">
							<div class="layui-input-wrap">
								<div class="layui-input-prefix">
									<i class="layui-icon layui-icon-vercode"></i>
								</div>
								<input
									type="text"
									name="smsCode"
									placeholder="请输入短信验证码"
									class="layui-input"
									lay-verify="required"
								/>
								<button type="button" class="layui-btn layui-btn-primary" id="smsBtn">
									获取验证码
								</button>
							</div>
						</div>
						<button class="layui-btn btn-next" lay-submit lay-filter="step2Submit">下一步</button>
					</form>
				</div>

				<!-- 步骤3：设置新密码 -->
				<div class="step-pane" id="step3">
					<form class="layui-form" lay-filter="passwordForm">
						<div class="layui-form-item">
							<div class="layui-input-wrap">
								<div class="layui-input-prefix">
									<i class="layui-icon layui-icon-password"></i>
								</div>
								<input
									type="password"
									name="password"
									placeholder="新的密码"
									class="layui-input"
									lay-verify="required|password"
									lay-affix="eye"
								/>
							</div>
						</div>
						<div class="layui-form-item">
							<div class="layui-input-wrap">
								<div class="layui-input-prefix">
									<i class="layui-icon layui-icon-password"></i>
								</div>
								<input
									type="password"
									name="confirmPassword"
									placeholder="确认密码"
									class="layui-input"
									lay-verify="required|confirmPassword"
									lay-affix="eye"
								/>
							</div>
						</div>
						<div class="password-rules">
							说明：密码要求8位及以上长度，必须包含字母、数字、特殊字符3种组合。特殊字符包含@#$%^&*，不包含空格。
						</div>
						<button class="layui-btn btn-submit" lay-submit lay-filter="step3Submit">确定</button>
					</form>
				</div>

				<!-- 步骤4：修改成功 -->
				<div class="step-pane" id="step4">
					<div class="success-message">
						<i class="layui-icon layui-icon-ok-circle"></i>
						<h3>密码修改成功</h3>
						<p>您的密码已经成功修改，请使用新密码登录系统</p>
						<a href="login.html" class="layui-btn">返回登录</a>
					</div>
				</div>
			</div>
		</div>

		<script src="layui/layui.js"></script>
		<script>
			$(document).ready(function () {
				layui.use(['form', 'layer'], function () {
					var form = layui.form;
					var layer = layui.layer;
					var currentStep = 1;

					// 验证规则
					form.verify({
						// 密码验证规则
						password: function (value) {
							if (value.length < 8) {
								return '密码长度不能小于8位';
							}

							var hasLetter = /[a-zA-Z]/.test(value);
							var hasNumber = /[0-9]/.test(value);
							var hasSpecial = /[@#$%^&*]/.test(value);
							var hasSpace = /\s/.test(value);

							if (hasSpace) {
								return '密码不能包含空格';
							}

							var typeCount = 0;
							if (hasLetter) typeCount++;
							if (hasNumber) typeCount++;
							if (hasSpecial) typeCount++;

							if (typeCount < 3) {
								return '密码必须包含字母、数字、特殊字符(@#$%^&*)三种组合';
							}
						},

						// 确认密码验证
						confirmPassword: function (value) {
							var password = $('input[name="password"]').val();
							if (value !== password) {
								return '两次输入的密码不一致';
							}
						},
					});

					// 步骤1提交
					form.on('submit(step1Submit)', function (data) {
						// 模拟验证用户名
						var username = data.field.username;
						if (username) {
							// 这里可以添加实际的用户名验证逻辑
							// 模拟验证成功
							goToStep(2);
						}
						return false; // 阻止表单默认提交
					});

					// 步骤2提交
					form.on('submit(step2Submit)', function (data) {
						// 模拟验证短信验证码
						var smsCode = data.field.smsCode;
						if (smsCode) {
							// 这里可以添加实际的验证码验证逻辑
							// 模拟验证成功
							if (smsCode === '123456') {
								goToStep(3);
							} else {
								layer.msg('验证码错误', { icon: 2 });
							}
						}
						return false; // 阻止表单默认提交
					});

					// 步骤3提交
					form.on('submit(step3Submit)', function (data) {
						// 模拟密码修改
						var loadIndex = layer.load(1, { shade: [0.3, '#000'] });

						// 模拟网络请求延迟
						setTimeout(function () {
							layer.close(loadIndex);
							// 显示成功页面
							goToStep(4);
						}, 1000);

						return false; // 阻止表单默认提交
					});

					// 短信验证码倒计时
					var smsCountdown = 0;
					function startSmsCountdown() {
						smsCountdown = 120;
						var $btn = $('#smsBtn').prop('disabled', true);
						var timer = setInterval(function () {
							$btn.text(--smsCountdown + '秒后可重发');
							if (smsCountdown <= 0) {
								clearInterval(timer);
								$btn.prop('disabled', false).text('获取验证码');
							}
						}, 1000);
					}

					// 获取验证码按钮点击事件
					$('#smsBtn').on('click', function () {
						// 模拟发送验证码
						layer.msg('验证码已发送', { icon: 1 });
						startSmsCountdown();
					});

					// 切换到指定步骤
					function goToStep(step) {
						// 更新步骤指示器
						$('.step-circle').removeClass('active completed');
						$('.step-line').removeClass('active');

						// 设置当前步骤为激活状态
						$('.step-item[data-step="' + step + '"] .step-circle').addClass('active');

						// 设置之前的步骤为已完成状态
						for (var i = 1; i < step; i++) {
							$('.step-item[data-step="' + i + '"] .step-circle').addClass('completed');
							if (i < step - 1) {
								$('.step-item[data-step="' + i + '"]')
									.next('.step-line')
									.addClass('active');
							}
						}

						// 隐藏所有步骤内容，显示当前步骤
						$('.step-pane').removeClass('active');
						$('#step' + step).addClass('active');

						// 更新当前步骤
						currentStep = step;
					}

					// 键盘事件
					$(document).on('keydown', function (e) {
						if (e.keyCode === 13) {
							var $btn = $('.step-pane.active button[lay-submit]');
							if ($btn.length) $btn.click();
							return false;
						}
					});
				});
			});
		</script>
	</body>
</html>
