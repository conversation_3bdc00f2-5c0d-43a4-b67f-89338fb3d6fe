<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1.0"
  >
  <title>首页</title>
  <link
    rel="stylesheet"
    href="layui/css/layui.css"
  >
  <link
    rel="stylesheet"
    href="style/home.css"
  >
  <script src="tools/jquery-3.7.1.min.js"></script>
</head>

<body>
  <div
    class="home-container"
    id="homeContainer"
  >
  </div>

  <script src="layui/layui.js"></script>
  <script>
    // 定义各个板块的数据
    const sectionData = {
      // 办文模块数据
      document: {
        title: "办文",
        moreLink: "javascript:;",
        iconColor: "blue",
        items: [
          { icon: "layui-icon-file-b", title: "新建发文", badge: 0 },
          { icon: "layui-icon-form", title: "发文办理", badge: 3 },
          { icon: "layui-icon-read", title: "新建收文", badge: 0 },
          { icon: "layui-icon-tabs", title: "收文办理", badge: 3 },
          { icon: "layui-icon-star", title: "呈批件", badge: 99 },
          { icon: "layui-icon-template", title: "公文收藏", badge: 0 },
          { icon: "layui-icon-transfer", title: "文件传阅", badge: 0 },
          { icon: "layui-icon-search", title: "公文检索", badge: 0 }
        ]
      },

      // 办事模块数据
      task: {
        title: "办事",
        moreLink: "javascript:;",
        iconColor: "green",
        items: [
          { icon: "layui-icon-date", title: "日程管理", badge: 0 },
          { icon: "layui-icon-log", title: "领导日程维护", badge: 0 },
          { icon: "layui-icon-note", title: "处室日程维护", badge: 0 },
          { icon: "layui-icon-list", title: "厅务活动维护", badge: 0 }
        ]
      },

      // 办会模块数据
      meeting: {
        title: "办会",
        moreLink: "javascript:;",
        iconColor: "orange",
        items: [
          { icon: "layui-icon-group", title: "会议室申请", badge: 0 },
          { icon: "layui-icon-survey", title: "会议室审核", badge: 3 }
        ]
      }
    };

    /**
     * 生成单个模块项的HTML
     * @param {Object} item - 模块项数据
     * @param {string} iconColor - 图标颜色类名
     * @returns {string} 模块项HTML
     */
    function generateModuleItemHTML (item, iconColor) {
      const badgeHTML = item.badge > 0 ? `<div class="badge">${item.badge > 99 ? '99' : item.badge}</div>` : '';
      //这里用的是layui的图标 也可以直接替换为icon图片地址src="./icon/xxx.png" 然后标签替换为img
      return `
    <div class="module-item">
      <div class="module-icon ${iconColor}">
        <i class="layui-icon ${item.icon}"></i>
      </div>
      <div class="module-title">${item.title}</div>
      ${badgeHTML}
    </div>
  `;
    }

    /**
     * 生成板块HTML
     * @param {Object} section - 板块数据
     * @returns {string} 板块HTML
     */
    function generateSectionHTML (section) {
      let itemsHTML = '';

      section.items.forEach(item => {
        itemsHTML += generateModuleItemHTML(item, section.iconColor);
      });

      return `
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">${section.title}</h2>
        <a href="${section.moreLink}" class="more-link">更多 &gt;</a>
      </div>
      <div class="module-grid">
        ${itemsHTML}
      </div>
    </div>
  `;
    }

    /**
     * 动态加载所有板块到页面
     */
    function loadAllSections () {
      const container = $('#homeContainer');
      if (!container) return;

      // 清空容器
      container.html('');

      let html = '';
      // 加载办文模块
      html += generateSectionHTML(sectionData.document);

      // 加载办事模块
      html += generateSectionHTML(sectionData.task);

      // 加载办会模块
      html += generateSectionHTML(sectionData.meeting);

      container.html(html);

      // 重新绑定点击事件
      bindModuleClickEvents();
    }

    /**
     * 绑定模块点击事件
     */
    function bindModuleClickEvents () {
      layui.use(['layer'], function () {
        const layer = layui.layer;

        // 模块点击事件
        $('.module-item').on('click', function () {
          const title = $(this).find('.module-title').text();
          layer.msg('您点击了【' + title + '】模块', { icon: 1 });
        });
      });
    }

    // 页面加载完成后初始化
    $(document).ready(function () {
      loadAllSections();
    }); 
  </script>
</body>

</html>