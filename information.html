<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1.0"
  >
  <title>咨询页面</title>
  <link
    rel="stylesheet"
    href="layui/css/layui.css"
  >
  <link
    rel="stylesheet"
    href="./style/information.css"
  >
  </link>
</head>

<body>

  <div class="main-container">
    <!-- 轮播图区域 -->
    <div class="grid-item carousel-container">
      <div
        class="layui-carousel"
        id="carousel"
      >
        <div carousel-item>
          <div style="background: url('https://ts3.tc.mm.bing.net/th/id/OIP-C.ZkoPhpKfJwsvGmpm8RsragHaFp?rs=1&pid=ImgDetMain&o=7&rm=3') center/cover; height: 100%; position: relative;">
            <div class="carousel-title-container">
              <div class="carousel-title">
                <h3>世界银行来湘开展湘西边区攻坚项目前期调研</h3>
              </div>
              <div class="carousel-indicator-space"></div>
            </div>
          </div>
          <div style="background: url('') center/cover; height: 100%; position: relative;">
            <div class="carousel-title-container">
              <div class="carousel-title">
                <h3>我省顺利举行2025年度会计初、高级资格考试</h3>
              </div>
              <div class="carousel-indicator-space"></div>
            </div>
          </div>
          <div style="background: url('') center/cover; height: 100%; position: relative;">
            <div class="carousel-title-container">
              <div class="carousel-title">
                <h3>省财政厅召开2025年度预算管理一体化工作会议</h3>
              </div>
              <div class="carousel-indicator-space"></div>
            </div>
          </div>
          <div style="background: url('') center/cover; height: 100%; position: relative;">
            <div class="carousel-title-container">
              <div class="carousel-title">
                <h3>关于加强财政专项资金管理的通知</h3>
              </div>
              <div class="carousel-indicator-space"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作动态区域 -->
    <div class="grid-item">
      <div class="card-header">
        <span class="header-title">资讯动态</span>
        <a
          href="#"
          class="more-link"
        >更多</a>
      </div>
      <div class="card-body">
        <div
          class="layui-tab layui-tab-brief"
          lay-filter="news-tab"
        >
          <ul class="layui-tab-title">
            <li class="layui-this">工作动态</li>
            <li>机关党建</li>
            <li>财源建设</li>
            <li>预算管理一体化</li>
            <li>财经</li>
          </ul>
          <div class="layui-tab-content">
            <!-- 工作动态内容 -->
            <div
              class="layui-tab-item layui-show"
              id="tab-work"
            >
            </div>
            <!-- 机关党建内容 -->
            <div
              class="layui-tab-item"
              id="tab-party"
            >
            </div>

            <!-- 财源建设内容 -->
            <div
              class="layui-tab-item"
              id="tab-finance"
            >
            </div>

            <!-- 预算管理一体化内容 -->
            <div
              class="layui-tab-item"
              id="tab-budget"
            >
            </div>

            <!-- 财经内容 -->
            <div
              class="layui-tab-item"
              id="tab-economy"
            >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 厅务活动区域 -->
    <div class="grid-item">
      <div class="card-header">
        <span class="header-title">厅务活动</span>
        <span>2025年6月12日</span>
      </div>
      <div class="card-body">
        <div
          class="layui-tab"
          lay-filter="activity-tab"
        >
          <ul class="layui-tab-title">
            <li class="layui-this">领导日程安排</li>
            <li>处室日程安排</li>
            <li>近期工作日程预告</li>
          </ul>
          <div class="layui-tab-content">
            <!-- 领导日程安排 -->
            <div
              class="layui-tab-item layui-show"
              id="tab-leader"
            >
            </div>
            <!-- 处室日程安排 -->
            <div
              class="layui-tab-item"
              id="tab-department"
            >
            </div>
            <!-- 近期工作日程预告 -->
            <div
              class="layui-tab-item"
              id="tab-schedule"
            >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 电子公告区域 -->
    <div class="grid-item">
      <div class="card-header">
        <span class="header-title">电子公告</span>
        <a
          href="#"
          class="more-link"
        >更多</a>
      </div>
      <div class="card-body">
        <ul class="news-list">
        </ul>
      </div>
    </div>
  </div>

  <script src="layui/layui.js"></script>
  <script src="./tools/jquery-3.7.1.min.js"></script>
  <script>
    // 数据模拟API
    const MockAPI = {
      // 资讯数据
      getNewsData (category = 'work') {
        const newsData = {
          work: [
            { id: 1, title: "我省顺利举行2025年度会计初、高级资格考试", date: "2025-05-28", link: "#" },
            { id: 2, title: "省财政厅召开2025年度预算管理一体化工作会议", date: "2025-05-25", link: "#" },
            { id: 3, title: "关于加强财政专项资金管理的通知", date: "2025-05-22", link: "#" },
            { id: 4, title: "财政厅组织开展财政资金使用情况专项检查", date: "2025-05-20", link: "#" },
            { id: 5, title: "2025年第二季度财政收支情况分析会召开", date: "2025-05-18", link: "#" },
            { id: 6, title: "全省财政系统党风廉政建设工作会议召开", date: "2025-05-15", link: "#" }
          ],
          party: [
            { id: 1, title: "开展\"不忘初心、牢记使命\"主题教育", date: "2025-06-05", link: "#" },
            { id: 2, title: "财政厅党组召开2025年第三季度党建工作会议", date: "2025-06-02", link: "#" },
            { id: 3, title: "举办2025年度党务工作者培训班", date: "2025-05-28", link: "#" },
            { id: 4, title: "财政厅机关党委开展党风廉政建设宣讲活动", date: "2025-05-25", link: "#" },
            { id: 5, title: "深入学习贯彻党的二十大精神", date: "2025-05-20", link: "#" }
          ],
          finance: [
            { id: 1, title: "关于促进实体经济发展壮大财源的实施意见", date: "2025-06-08", link: "#" },
            { id: 2, title: "我省出台财源建设三年规划", date: "2025-06-01", link: "#" },
            { id: 3, title: "财政支持工业互联网发展专项资金实施细则", date: "2025-05-27", link: "#" },
            { id: 4, title: "财政厅组织召开财源建设工作推进会", date: "2025-05-20", link: "#" },
            { id: 5, title: "关于落实财源建设工作考核办法的通知", date: "2025-05-15", link: "#" }
          ],
          budget: [
            { id: 1, title: "全省预算管理一体化系统操作培训通知", date: "2025-06-10", link: "#" },
            { id: 2, title: "关于做好2026年预算编制工作的通知", date: "2025-06-05", link: "#" },
            { id: 3, title: "预算管理一体化系统升级公告", date: "2025-05-30", link: "#" },
            { id: 4, title: "省财政厅召开2025年度预算管理一体化工作会议", date: "2025-05-25", link: "#" },
            { id: 5, title: "关于加强预算单位财务管理的通知", date: "2025-05-20", link: "#" }
          ],
          economy: [
            { id: 1, title: "2025年一季度全省财政经济运行情况分析", date: "2025-06-08", link: "#" },
            { id: 2, title: "关于进一步深化财税体制改革的意见", date: "2025-06-01", link: "#" },
            { id: 3, title: "我省出台促进经济高质量发展的财政政策", date: "2025-05-25", link: "#" },
            { id: 4, title: "全省经济工作会议召开 部署2025年财政工作", date: "2025-05-18", link: "#" },
            { id: 5, title: "财政厅发布2025年财政经济形势分析报告", date: "2025-05-10", link: "#" }
          ]
        };
        return newsData[category] || [];
      },

      // 公告数据
      getAnnouncementData () {
        return [
          { id: 1, title: "感谢信（海南旅游发展大会组委会）", date: "2025-06-10", link: "#" },
          { id: 2, title: "关于组织2025年度会计专业技术资格考试的通知", date: "2025-06-08", link: "#" },
          { id: 3, title: "关于加强财政专项资金管理的通知", date: "2025-06-05", link: "#" },
          { id: 4, title: "任前公示（关于张三等同志任职的公示）", date: "2025-06-03", link: "#" },
          { id: 5, title: "关于开展2025年度财政系统业务培训的通知", date: "2025-06-01", link: "#" },
          { id: 6, title: "关于调整部分财政专项资金使用范围的通知", date: "2025-05-28", link: "#" },
          { id: 7, title: "关于开展财政资金使用情况专项检查的通知", date: "2025-05-25", link: "#" },
        ];
      },

      // 活动数据
      getActivityData (type = 'leader') {
        const activityData = {
          leader: [
            { name: "张 三", role: "出差天津同步加强财政市财务局25年度上半年财政数据" },
            { name: "李 四", role: "厅内办公" },
            { name: "陈x鑫", role: "厅内办公" },
            { name: "王 五", role: "厅内办公" },
            { name: "赵 六", role: "厅内办公" },
            { name: "孙 七", role: "厅内办公" }
          ],
          department: [
            { name: "预算处", role: "召开2025年度预算编制工作会议" },
            { name: "会计处", role: "组织会计专业技术资格考试培训" },
            { name: "国库处", role: "审核各部门资金拨付申请" },
            { name: "行政处", role: "组织厅内安全检查" },
            { name: "人事处", role: "组织新员工入职培训" }
          ],
          schedule: [
            { name: "6月15日", role: "全省财政系统工作会议" },
            { name: "6月18日", role: "2025年度预算执行情况分析会" },
            { name: "6月20日", role: "财政专项资金使用情况检查" },
            { name: "6月25日", role: "厅党组扩大会议" },
            { name: "6月30日", role: "上半年工作总结会" }
          ]
        };
        return activityData[type] || [];
      }
    };

    // 数据渲染函数 - jQuery优化版本
    const DataRenderer = {
      // 渲染资讯列表
      renderNewsList (data, containerId) {
        const $container = $('#' + containerId);
        if ($container.length === 0) {
          console.warn(`容器 #${containerId} 未找到`);
          return;
        }

        if (!Array.isArray(data) || data.length === 0) {
          $container.html('<div class="no-data">暂无数据</div>');
          return;
        }

        const $newsList = $('<ul class="news-list"></ul>');

        $.each(data, function (index, item) {
          const $newsItem = $(`
            <li class="news-item">
              <a href="${item.link}" class="news-title">${item.title}</a>
              <span class="news-date">${item.date}</span>
            </li>
          `);
          $newsList.append($newsItem);
        });

        $container.empty().append($newsList);
      },

      // 渲染活动列表
      renderActivityList (data, containerId) {
        const $container = $('#' + containerId);
        if ($container.length === 0) {
          console.warn(`容器 #${containerId} 未找到`);
          return;
        }

        if (!Array.isArray(data) || data.length === 0) {
          $container.html('<div class="no-data">暂无数据</div>');
          return;
        }

        const $activityList = $('<ul class="activity-list"></ul>');

        $.each(data, function (index, item) {
          const $activityItem = $(`
            <li class="activity-item">
              <span class="activity-name">${item.name}</span>
              <span class="activity-desc">${item.role}</span>
            </li>
          `);
          $activityList.append($activityItem);
        });

        $container.empty().append($activityList);
      }
    };
  </script>

  <script>
    layui.use(['carousel', 'element'], function () {
      var carousel = layui.carousel;
      var element = layui.element;

      // 初始化轮播图
      carousel.render({
        elem: '#carousel',
        width: '100%',
        height: '100%',
        interval: 5000,
        anim: 'fade',
        arrow: 'hover',
        indicator: 'inside'
      });

      // 监听资讯tab切换事件
      var tabNewsCategories = ['work', 'party', 'finance', 'budget', 'economy'];
      element.on('tab(news-tab)', function (data) {
        var category = tabNewsCategories[data.index];
        var targetId = 'tab-' + category;
        // 根据选中的标签加载对应数据
        var newsData = MockAPI.getNewsData(category);
        DataRenderer.renderNewsList(newsData, targetId);
      });
      //默认请求第一个tab数据
      DataRenderer.renderNewsList(MockAPI.getNewsData(tabNewsCategories[0]), 'tab-' + tabNewsCategories[0]);

      // 监听厅务活动选项卡切换事件
      var tabActivityCategories = ['leader', 'department', 'schedule'];
      element.on('tab(activity-tab)', function (data) {
        var category = tabActivityCategories[data.index];
        var targetId = 'tab-' + category
        // 根据选中的标签加载对应数据
        var activityData = MockAPI.getActivityData(category);
        DataRenderer.renderActivityList(activityData, targetId);
      });
      //默认请求第一个tab数据
      DataRenderer.renderActivityList(MockAPI.getActivityData(tabActivityCategories[0]), 'tab-' + tabActivityCategories[0]);
      // 页面初始化数据加载 - jQuery优化版本
      function initializeData () {
        try {
          // 加载公告数据
          const announcementData = MockAPI.getAnnouncementData();
          const $announcementContainer = $('.grid-item:last-child .card-body');

          if ($announcementContainer.length > 0) {
            if (Array.isArray(announcementData) && announcementData.length > 0) {
              const $newsList = $('<ul class="news-list"></ul>');

              $.each(announcementData, function (index, item) {
                const $newsItem = $(`
                  <li class="news-item">
                    <a href="${item.link}" class="news-title">${item.title}</a>
                    <span class="news-date">${item.date}</span>
                  </li>
                `);
                $newsList.append($newsItem);
              });

              $announcementContainer.empty().append($newsList);
            } else {
              $announcementContainer.html('<div class="no-data">暂无公告数据</div>');
            }
          } else {
            console.warn('公告容器未找到');
          }
        } catch (error) {
          console.error('初始化数据时发生错误:', error);
        }
      }

      // 页面加载完成后执行 - jQuery优化版本
      $(document).ready(function () {
        // 重新渲染页面元素，确保所有组件正确显示
        element.render('tab');

        // 初始化数据
        initializeData();
      });
    });
  </script>
</body>

</html>