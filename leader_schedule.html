<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>领导日程管理</title>

		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="./style/leader_schedule.css" />
		<script src="./tools/jquery-3.7.1.min.js"></script>
	</head>

	<body>
		<div class="top-tabs-container">
			<div class="top-tabs">
				<div class="tab-item active" data-tab="leader">领导日程</div>
				<div class="tab-item" data-tab="department">处室日程</div>
				<div class="tab-item" data-tab="personal">个人日程</div>
			</div>
		</div>

		<div class="schedule-container">
			<!-- 左侧日程列表 -->
			<div class="schedule-sidebar">
				<div class="sidebar-header">
					<div class="sidebar-title-bar">
						<h3 class="sidebar-title">日程安排</h3>
						<button type="button" class="add-schedule-btn" id="addScheduleBtn">
							<i class="layui-icon layui-icon-add-1"></i>
						</button>
					</div>
				</div>

				<div class="sidebar-content" id="scheduleList"></div>
			</div>

			<!-- 右侧日历区域 -->
			<div class="calendar-main">
				<div class="calendar-header">
					<div class="calendar-left" style="display: flex; align-items: center">
						<div class="layui-form" style="margin: 0">
							<select id="leaderSelect" lay-filter="leaderSelect">
								<option value="">请选择领导</option>
								<option value="leader1">张主任</option>
								<option value="leader2">李副主任</option>
								<option value="leader3">王处长</option>
							</select>
						</div>
						<span style="color: #666; font-size: 13px; margin-left: 15px"
							>当月有<span id="monthScheduleCount">0</span>个日程安排</span
						>
					</div>
					<div class="calendar-title">
						<button class="layui-btn layui-btn-primary layui-btn-sm" id="prevMonth">
							<i class="layui-icon layui-icon-left"></i>
						</button>
						<input
							type="text"
							id="currentMonth"
							class="layui-input"
							placeholder="请选择年月"
							readonly
						/>
						<button class="layui-btn layui-btn-primary layui-btn-sm" id="nextMonth">
							<i class="layui-icon layui-icon-right"></i>
						</button>
					</div>
					<div class="calendar-actions">
						<div class="dropdown-container">
							<button class="layui-btn layui-btn-primary layui-btn-sm" id="exportDropdownBtn">
								<i class="layui-icon layui-icon-export"></i> 导入导出
								<i class="layui-icon layui-icon-down"></i>
							</button>
							<div class="dropdown-menu" id="exportDropdownMenu">
								<div class="dropdown-item" data-action="import">
									<i class="layui-icon layui-icon-upload"></i> 导入会议活动
								</div>
								<div class="dropdown-item" data-action="export-meetings">
									<i class="layui-icon layui-icon-export"></i> 导出会议活动
								</div>
								<div class="dropdown-item" data-action="export-records">
									<i class="layui-icon layui-icon-file"></i> 导出记录
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="calendar-body">
					<div id="calendar"></div>
				</div>
			</div>
		</div>

		<!-- 新建按钮 -->
		<button class="fab-button" id="fabAddBtn">
			<i class="layui-icon layui-icon-add-1"></i>
		</button>

		<script src="./fullcalendar-scheduler/index.global.min.js"></script>
		<script src="layui/layui.js"></script>

		<script>
			// 全局变量
			let calendar;
			let currentTab = 'leader';
			// tabmap映射
			const tabMap = {
				leader: '领导日程',
				department: '处室日程',
				personal: '个人日程',
			};
			// 下拉框选项配置
			const selectOptions = {
				// 领导选项
				leaders: [
					{ value: '', text: '请选择' },
					{ value: 'leader1', text: '张主任' },
					{ value: 'leader2', text: '李副主任' },
					{ value: 'leader3', text: '王处长' },
				],

				// 时间选项
				timeSlots: [
					'08:00',
					'08:30',
					'09:00',
					'09:30',
					'10:00',
					'10:30',
					'11:00',
					'11:30',
					'12:00',
					'12:30',
					'13:00',
					'13:30',
					'14:00',
					'14:30',
					'15:00',
					'15:30',
					'16:00',
					'16:30',
					'17:00',
					'17:30',
					'18:00',
					'18:30',
				],

				// 会议类型选项
				meetingTypes: [
					{ value: '', text: '请选择' },
					{ value: 'internal', text: '内部会议' },
					{ value: 'external', text: '外部会议' },
					{ value: 'video', text: '视频会议' },
					{ value: 'training', text: '培训会议' },
				],

				// 部门选项
				departments: [
					{ value: '', text: '请选择' },
					{ value: 'office', text: '办公室' },
					{ value: 'hr', text: '人事处' },
					{ value: 'finance', text: '财务处' },
					{ value: 'tech', text: '技术处' },
				],

				// 日程类型选项
				scheduleTypes: [
					{ value: 'meeting', text: '会议活动' },
					{ value: 'activity', text: '活动安排' },
					{ value: 'personal', text: '个人事务' },
					{ value: 'important', text: '重要事项' },
				],

				// 短信类型选项
				smsTypes: [
					{ value: 'timely', text: '及时提醒' },
					{ value: 'scheduled', text: '定时提醒' },
				],
			};

			// 获取选项文本的辅助函数
			function getOptionText(optionType, value) {
				const options = selectOptions[optionType];
				if (!options) return value || '-';

				if (optionType === 'timeSlots') {
					return value || '-';
				}

				const option = options.find((opt) => opt.value === value);
				return option ? option.text : value || '-';
			}

			// 生成下拉框选项HTML的函数
			function generateSelectOptions(optionType, selectedValue = '', includeEmpty = true) {
				const options = selectOptions[optionType];
				if (!options) return '';

				let html = '';

				if (optionType === 'timeSlots') {
					if (includeEmpty) {
						html += `<option value="">请选择时间</option>`;
					}
					options.forEach((time) => {
						const selected = selectedValue === time ? 'selected' : '';
						html += `<option value="${time}" ${selected}>${time}</option>`;
					});
				} else {
					options.forEach((option) => {
						const selected = selectedValue === option.value ? 'selected' : '';
						html += `<option value="${option.value}" ${selected}>${option.text}</option>`;
					});
				}

				return html;
			}

			// 生成时间选项（用于结束时间，需要过滤）
			function generateTimeOptions(selectedValue = '', startTime = '', isEndTime = false) {
				let html = '';
				const placeholder = isEndTime ? '请选择结束时间' : '请选择开始时间';
				html += `<option value="">${placeholder}</option>`;

				selectOptions.timeSlots.forEach((time) => {
					// 如果是结束时间，只显示晚于开始时间的选项
					if (isEndTime && startTime && time <= startTime) {
						return;
					}
					const selected = selectedValue === time ? 'selected' : '';
					html += `<option value="${time}" ${selected}>${time}</option>`;
				});

				return html;
			}

			// 格式化日程日期时间显示
			function formatScheduleDateTime(schedule, today) {
				const scheduleDate = schedule.scheduleDate || schedule.startDate || schedule.start;
				const endDate = schedule.endDate || scheduleDate;
				const isMultiDay = scheduleDate !== endDate;

				// 格式化日期
				let dateText = '';
				if (isMultiDay) {
					const startDate = new Date(scheduleDate);
					const endDateObj = new Date(endDate);
					const startMonth = startDate.getMonth() + 1;
					const startDay = startDate.getDate();
					const endMonth = endDateObj.getMonth() + 1;
					const endDay = endDateObj.getDate();

					dateText =
						startMonth === endMonth
							? `${startMonth}月${startDay}-${endDay}日`
							: `${startMonth}月${startDay}日-${endMonth}月${endDay}日`;
				} else {
					const scheduleDateTime = new Date(scheduleDate);
					const todayDate = new Date(today);
					const diffDays = Math.floor((scheduleDateTime - todayDate) / (1000 * 60 * 60 * 24));

					if (diffDays === 0) dateText = '今天';
					else if (diffDays === 1) dateText = '明天';
					else if (diffDays === 2) dateText = '后天';
					else if (diffDays === -1) dateText = '昨天';
					else {
						const month = scheduleDateTime.getMonth() + 1;
						const day = scheduleDateTime.getDate();
						dateText = `${month}月${day}日`;
					}
				}

				// 格式化时间
				let timeText = '时间待定';
				if (schedule.startTime && schedule.endTime) {
					timeText = isMultiDay
						? `${schedule.startTime}-次日${schedule.endTime}`
						: `${schedule.startTime}-${schedule.endTime}`;
				} else if (schedule.startTime) {
					timeText = schedule.startTime;
				}
				return `${dateText} ${timeText}`;
			}

			let scheduleData = {
				leader: [
					{
						id: 'l1',
						title: '完成季度总结',
						leader: 'leader1',
						scheduleDate: '2025-06-01',
						endDate: '2025-06-01',
						startTime: '14:00',
						endTime: '15:00',
						provinceLeader: '王副省长',
						location: '省政府会议室',
						meetingType: 'internal',
						content: '完成第二季度工作总结报告，分析各项指标完成情况',
						prepareDept: 'office',
						accompanyDept: 'hr',
						leaderOpinion: '总结内容详实，分析到位',
						materialContent: '请提前准备相关数据材料',
						leaderSmsType: 'timely',
						leaderSmsContent: '请按时参加季度总结会议',
						smsRemind: true,
						type: 'important',
						status: 'completed',
						description: '完成第二季度工作总结报告',
					},
					{
						id: 'l2',
						title: '团队周会准备',
						leader: 'leader2',
						scheduleDate: '2025-06-02',
						endDate: '2025-06-02',
						startTime: '15:00',
						endTime: '16:00',
						provinceLeader: '',
						location: '办公楼三楼会议室',
						meetingType: 'internal',
						content: '准备下周团队会议议程和材料，讨论项目进展',
						prepareDept: 'office',
						accompanyDept: 'tech',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'meeting',
						status: 'pending',
						description: '准备下周团队会议议程和材料',
					},
					{
						id: 'l3',
						title: '跨天培训会议',
						leader: 'leader1',
						scheduleDate: '2025-06-03',
						endDate: '2025-06-04',
						startTime: '17:00',
						endTime: '10:00',
						provinceLeader: '李副省长',
						location: '培训中心',
						meetingType: 'training',
						content: '为期两天的管理培训会议，包含理论学习和实践操作',
						prepareDept: '',
						accompanyDept: '',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'activity',
						status: 'pending',
						description: '为期两天的管理培训会议',
					},
					{
						id: 'l4',
						title: '项目讨论会',
						leader: 'leader3',
						scheduleDate: '2025-06-05',
						endDate: '2025-06-05',
						startTime: '10:00',
						endTime: '12:00',
						provinceLeader: '李副省长',
						location: '技术部会议室',
						meetingType: 'external',
						content: '与技术团队讨论新项目方案，确定技术路线',
						prepareDept: 'tech',
						accompanyDept: 'office',
						leaderOpinion: '方案可行，建议尽快实施',
						materialContent: '请准备技术方案详细说明',
						leaderSmsType: 'scheduled',
						leaderSmsContent: '明日项目讨论会，请准时参加',
						smsRemind: true,
						type: 'meeting',
						status: 'pending',
						description: '与技术团队讨论新项目方案',
					},
					{
						id: 'l5',
						title: '学习新框架',
						leader: 'leader2',
						scheduleDate: '2025-06-07',
						endDate: '2025-06-07',
						startTime: '14:00',
						endTime: '16:00',
						provinceLeader: '',
						location: '培训室',
						meetingType: 'training',
						content: '学习最新的前端开发框架，提升技术能力',
						prepareDept: 'tech',
						accompanyDept: '',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'activity',
						status: 'pending',
						description: '学习最新的前端开发框架',
					},
					{
						id: 'l6',
						title: '团队建设活动',
						leader: 'leader1',
						scheduleDate: '2025-06-15',
						endDate: '2025-06-15',
						startTime: '14:00',
						endTime: '17:00',
						provinceLeader: '',
						location: '户外拓展基地',
						meetingType: 'external',
						content: '组织团队户外拓展活动，增强团队凝聚力',
						prepareDept: 'hr',
						accompanyDept: 'office',
						leaderOpinion: '活动有意义，有助于团队建设',
						materialContent: '请提前联系拓展基地，确认活动安排',
						leaderSmsType: 'scheduled',
						leaderSmsContent: '团队建设活动通知，请准时参加',
						smsRemind: true,
						type: 'activity',
						status: 'pending',
						description: '组织团队户外拓展活动',
					},
				],
				department: [
					{
						id: 'd1',
						title: '部门月度会议',
						leader: 'leader1',
						scheduleDate: '2025-06-10',
						endDate: '2025-06-10',
						startTime: '09:00',
						endTime: '11:00',
						provinceLeader: '',
						location: '部门会议室',
						meetingType: 'internal',
						content: '部门月度工作总结会议，汇报各科室工作进展',
						prepareDept: 'office',
						accompanyDept: 'hr',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'meeting',
						status: 'pending',
						description: '部门月度工作总结会议',
					},
					{
						id: 'd2',
						title: '季度培训计划',
						leader: 'leader2',
						scheduleDate: '2025-06-20',
						endDate: '2025-06-20',
						startTime: '14:00',
						endTime: '16:00',
						provinceLeader: '',
						location: '培训中心',
						meetingType: 'training',
						content: '制定下季度员工培训计划，提升整体素质',
						prepareDept: 'hr',
						accompanyDept: 'office',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'activity',
						status: 'pending',
						description: '制定下季度员工培训计划',
					},
				],
				personal: [
					{
						id: 'p1',
						title: '健身计划',
						leader: 'leader1',
						scheduleDate: '2025-06-08',
						endDate: '2025-06-08',
						startTime: '18:00',
						endTime: '19:30',
						provinceLeader: '',
						location: '健身房',
						meetingType: '',
						content: '每周三次健身房锻炼，保持身体健康',
						prepareDept: '',
						accompanyDept: '',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'personal',
						status: 'pending',
						description: '每周三次健身房锻炼',
					},
					{
						id: 'p2',
						title: '家庭聚餐',
						leader: 'leader1',
						scheduleDate: '2025-06-12',
						endDate: '2025-06-12',
						startTime: '19:00',
						endTime: '21:00',
						provinceLeader: '',
						location: '家中',
						meetingType: '',
						content: '与家人共进晚餐，增进家庭感情',
						prepareDept: '',
						accompanyDept: '',
						leaderOpinion: '',
						materialContent: '',
						leaderSmsType: 'timely',
						leaderSmsContent: '',
						smsRemind: false,
						type: 'personal',
						status: 'pending',
						description: '与家人共进晚餐',
					},
				],
			};

			// 初始化
			$(document).ready(function () {
				initCalendar();
				initSidebar();
				bindEvents();
				renderScheduleList();
				updateMonthScheduleCount();
			});

			// 更新日期选择器的值
			function updateDatePickerValue() {
				if (!calendar) return;

				const currentDate = calendar.getDate();
				const year = currentDate.getFullYear();
				const month = currentDate.getMonth() + 1;
				const formattedDate = `${year}年${month.toString().padStart(2, '0')}月`;
				$('#currentMonth').val(formattedDate);
			}

			// 初始化日历 -
			function initCalendar() {
				const calendarEl = $('#calendar')[0];

				calendar = new FullCalendar.Calendar(calendarEl, {
					initialView: 'dayGridMonth',
					locale: 'zh-cn',
					headerToolbar: false,
					height: '100%',
					dayMaxEvents: 3,
					moreLinkText: '......',

					// 事件数据
					events: function (info, successCallback) {
						const events = [];

						scheduleData[currentTab].forEach((item) => {
							const startDate = item.scheduleDate || item.startDate || item.start;
							const endDate = item.endDate || startDate;

							// 如果是跨天日程，为每一天创建一个事件
							if (startDate !== endDate) {
								const start = new Date(startDate);
								const end = new Date(endDate);

								// 为跨天日程的每一天创建事件
								for (let d = start; d <= end; d.setDate(d.getDate() + 1)) {
									const currentDate = d.toISOString().split('T')[0];
									events.push({
										id: `${item.id}_${currentDate}`,
										title: `${item.title || '未命名日程'} (跨天)`,
										start: currentDate,
										end: currentDate,
										extendedProps: {
											originalId: item.id,
											type: item.type,
											description: item.description || item.content,
											status: item.status,
											startTime: item.startTime,
											endTime: item.endTime,
											location: item.location,
											isMultiDay: true,
											originalStartDate: startDate,
											originalEndDate: endDate,
										},
									});
								}
							} else {
								// 单天日程
								events.push({
									id: item.id,
									title: item.title || '未命名日程',
									start: startDate,
									end: startDate,
									extendedProps: {
										originalId: item.id,
										type: item.type,
										description: item.description || item.content,
										status: item.status,
										startTime: item.startTime,
										endTime: item.endTime,
										location: item.location,
										isMultiDay: false,
									},
								});
							}
						});

						successCallback(events);
					},

					// 点击日期创建事件
					dateClick: function (info) {
						showScheduleModal(info.dateStr);
					},

					// 点击事件查看详情
					eventClick: function (info) {
						const originalId = info.event.extendedProps.originalId || info.event.id;
						const schedule = findScheduleById(originalId);
						if (schedule) {
							showScheduleDetail(schedule);
						}
					},

					// 月份变化时更新日期控件的值
					datesSet: function (info) {
						updateDatePickerValue();
					},
				});

				calendar.render();
			}

			// 初始化侧边栏
			function initSidebar() {
				layui.use('form', function () {
					const form = layui.form;
					form.render();
				});
			}

			// 绑定事件
			function bindEvents() {
				// 初始化layui组件
				layui.use(['form', 'dropdown', 'laydate'], function () {
					const form = layui.form;
					const dropdown = layui.dropdown;
					const laydate = layui.laydate;

					// 初始化年月选择器
					laydate.render({
						elem: '#currentMonth',
						type: 'month',
						format: 'yyyy年MM月',
						value: new Date(),
						btns: ['now'],
						done: function (value, date) {
							// 当选择年月后，跳转到对应的月份
							if (calendar) {
								const targetDate = new Date(date.year, date.month - 1, 1);
								calendar.gotoDate(targetDate);
							}
						},
					});

					// 渲染表单组件
					form.render();

					// 监听领导选择(执行逻辑)
					form.on('select(leaderSelect)', function (data) {
						console.log('选择的领导:', data.value);
					});

					// 初始化导入导出下拉菜单
					$('#exportDropdownBtn').on('click', function (e) {
						e.stopPropagation();
						const $menu = $('#exportDropdownMenu');
						$menu.toggleClass('show');
					});

					// 下拉菜单项点击事件
					$('.dropdown-item').on('click', function () {
						const action = $(this).data('action');
						const $menu = $('#exportDropdownMenu');
						$menu.removeClass('show');

						layui.use('layer', function () {
							const layer = layui.layer;
							let message = '';
							switch (action) {
								case 'import':
									message = '导入会议活动功能开发中...';
									break;
								case 'export-meetings':
									message = '导出会议活动功能开发中...';
									console.log(`output->scheduleData`, scheduleData);
									break;
								case 'export-records':
									message = '导出记录功能开发中...';
									break;
							}
							layer.msg(message, { icon: 1 });
						});
					});

					// 点击其他地方关闭下拉菜单
					$(document).on('click', function () {
						$('#exportDropdownMenu').removeClass('show');
					});
				});

				// 标签切换 事件委托
				$('.tab-item').on('click', function () {
					const tabType = $(this).data('tab');
					switchTab(tabType);
				});

				// 月份导航
				$('#prevMonth').on('click', function () {
					calendar.prev();
					// 导航后更新日期控件的值
					setTimeout(updateDatePickerValue, 100);
				});

				$('#nextMonth').on('click', function () {
					calendar.next();
					// 导航后更新日期控件的值
					setTimeout(updateDatePickerValue, 100);
				});

				// 新建日程按钮
				$('#fabAddBtn').on('click', function () {
					showScheduleModal();
				});

				$('#addScheduleBtn').on('click', function () {
					showScheduleModal();
				});
			}

			// 切换标签
			function switchTab(tabType) {
				currentTab = tabType;

				// 更新标签样式
				$('.tab-item').removeClass('active');
				$(`.tab-item[data-tab="${tabType}"]`).addClass('active');

				// 重新渲染日历和列表
				calendar.refetchEvents();
				renderScheduleList();
				updateMonthScheduleCount();
			}

			// 渲染日程列表
			function renderScheduleList() {
				const $scheduleList = $('#scheduleList');
				const schedules = scheduleData[currentTab];

				if (schedules.length === 0) {
					$scheduleList.html(`
          <div style="text-align: center; padding: 40px 20px; color: #999;">
            <i class="layui-icon layui-icon-face-cry" style="font-size: 48px; display: block; margin-bottom: 10px;"></i>
            <div>暂无日程安排</div>
          </div>
        `);
					return;
				}

				let html = '';
				const today = new Date().toISOString().split('T')[0];

				schedules.forEach((schedule) => {
					const typeText = getOptionText('scheduleTypes', schedule.type);
					const statusClass = schedule.status === 'completed' ? 'completed' : '';

					// 获取日程日期
					const scheduleDate = schedule.scheduleDate || schedule.startDate || schedule.start;
					const endDate = schedule.endDate || scheduleDate;
					const todayClass = scheduleDate === today ? 'today' : '';

					// 构建智能日期时间显示文本
					const dateTimeText = formatScheduleDateTime(schedule, today);

					html += `
          <div class="schedule-item ${statusClass} ${todayClass}" data-id="${schedule.id}">
            <div class="schedule-title">${schedule.title || '未命名日程'}</div>
            <div class="schedule-time">${dateTimeText}</div>
          </div>
        `;
				});

				$scheduleList.html(html);

				// 绑定点击事件 事件委托
				$scheduleList.off('click', '.schedule-item').on('click', '.schedule-item', function (e) {
					const scheduleId = $(this).data('id');
					console.log('点击日程项，ID:', scheduleId);

					const schedule = findScheduleById(scheduleId);
					console.log('找到的日程:', schedule);

					if (schedule) {
						showScheduleDetail(schedule);
					} else {
						console.error('未找到对应的日程数据');
					}
				});
			}
			// 根据ID查找日程
			function findScheduleById(id) {
				return scheduleData[currentTab].find((item) => item.id === id);
			}

			// 显示日程详情
			function showScheduleDetail(schedule) {
				console.log('显示日程详情，数据:', schedule);

				layui.use('layer', function () {
					const layer = layui.layer;
					console.log('layer模块加载成功');

					const content = `
          <div class="modal-container">
            <!-- 标题栏 -->
            <div class="modal-header">
                <h3>${(tabMap[currentTab] || '日程') + '详情'}</h3>
            </div>

            <!-- 详情内容区域 -->
            <div class="modal-content">
              <div class="modal-form-container">
                <table class="layui-table modal-table">
                  <tbody>
                    <tr>
                      <td class="label-cell">主题</td>
                      <td class="content-cell highlight">${schedule.title}</td>
                      <td class="label-cell">领导</td>
                      <td class="content-cell">${
												schedule.leader ? getOptionText('leaders', schedule.leader) : '-'
											}</td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始日期</td>
                      <td class="content-cell">${schedule.scheduleDate || '-'}</td>
                      <td class="label-cell">结束日期</td>
                      <td class="content-cell">${schedule.endDate || '-'}</td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始时间</td>
                      <td class="content-cell">${
												schedule.startTime ? getOptionText('timeSlots', schedule.startTime) : '-'
											}</td>
                      <td class="label-cell">结束时间</td>
                      <td class="content-cell">${
												schedule.endTime ? getOptionText('timeSlots', schedule.endTime) : '-'
											}</td>
                    </tr>
                    <tr>
                      <td class="label-cell">参会省领导</td>
                      <td class="content-cell">${schedule.provinceLeader || '-'}</td>
											<td class="label-cell">参会地点</td>
                      <td class="content-cell">${schedule.location || '-'}</td>
                    </tr>
                    <tr>
                      <td class="label-cell">会议类型</td>
                      <td class="content-cell">${
												schedule.meetingType
													? getOptionText('meetingTypes', schedule.meetingType)
													: '-'
											}</td>
											<td class="label-cell">会议内容</td>
                      <td class="content-cell" >${
												schedule.content || schedule.description || '-'
											}</td>
                    </tr>
                    <tr>
                      <td class="label-cell">材料准备处室</td>
                      <td class="content-cell">${
												schedule.prepareDept
													? getOptionText('departments', schedule.prepareDept)
													: '-'
											}</td>
                      <td class="label-cell">陪同参会处室</td>
                      <td class="content-cell">${
												schedule.accompanyDept
													? getOptionText('departments', schedule.accompanyDept)
													: '-'
											}</td>
                    </tr>
                    <tr>
                      <td class="label-cell textarea-cell">领导签批意见</td>
                      <td class="content-cell">${schedule.leaderOpinion || '-'}</td>
                      <td class="label-cell">材料短信提示</td>
                      <td class="content-cell">
                        <span class="status-tag ${schedule.smsRemind ? 'confirmed' : 'cancelled'}">
                          ${schedule.smsRemind ? '已启用' : '未启用'}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell textarea-cell">材料短信内容</td>
                      <td class="content-cell">${schedule.materialContent || '-'}</td>
                      <td class="label-cell">领导短信提示</td>
                      <td class="content-cell">
                          ${
														schedule.leaderSmsType
															? getOptionText('smsTypes', schedule.leaderSmsType)
															: '-'
													}
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">状态</td>
                      <td class="content-cell">
                        <span class="status-tag ${
													schedule.status === 'completed' ? 'completed' : 'pending'
												}">
                          ${schedule.status === 'completed' ? '已完成' : '进行中'}
                        </span>
                      </td>
                      <td class="label-cell">领导短信内容</td>
                      <td class="content-cell">${schedule.leaderSmsContent || '-'}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 固定在底部的按钮区域 -->
            <div class="modal-buttons">
              <button type="button" class="modal-btn modal-btn-default" onclick="layer.closeAll()">关闭</button>
              <button type="button" class="modal-btn modal-btn-primary" onclick="editSchedule('${
								schedule.id
							}')">编辑</button>
              <button type="button" class="modal-btn modal-btn-danger" onclick="deleteScheduleWithConfirm('${
								schedule.id
							}')">删除</button>
            </div>
          </div>
        `;

					window.currentDetailLayerIndex = layer.open({
						type: 1,
						title: false,
						area: ['920px', '600px'],
						content: content,
						shadeClose: false,
						resize: false,
					});
				});
			}

			// 显示新建/编辑日程弹窗
			function showScheduleModal(selectedDate = null, scheduleId = null) {
				layui.use(['layer', 'form'], function () {
					const layer = layui.layer;
					const form = layui.form;

					const isEdit = !!scheduleId;
					const schedule = isEdit ? findScheduleById(scheduleId) : null;
					const defaultDate =
						selectedDate || (schedule ? schedule.start : new Date().toISOString().split('T')[0]);

					const content = `
          <div class="modal-container">
            <!-- 标题栏 -->
            <div class="modal-header">
              <h3>${tabMap[currentTab] || '日程填写'}</h3>
            </div>

            <!-- 表单内容区域 -->
            <div class="modal-content">
              <form class="layui-form modal-form-container" lay-filter="leader_schedule_form" id="leader_schedule_form">
                <table class="layui-table modal-table">
                  <tbody>
                    <tr>
                      <td class="label-cell">领导 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="leader" lay-verify="required">
                          ${generateSelectOptions('leaders', schedule ? schedule.leader || '' : '')}
                        </select>
                      </td>
                      <td class="label-cell">主题<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="title" value="${
													schedule ? schedule.title || '' : ''
												}" placeholder="请输入"  lay-verify="required" class="layui-input">
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始日期<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="scheduleDate" value="${
													schedule ? schedule.scheduleDate : defaultDate
												}" placeholder="请选择开始日期" class="layui-input" lay-verify="required">
                      </td>
                      <td class="label-cell">结束日期<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="endDate" value="${
													schedule ? schedule.endDate : defaultDate
												}" placeholder="请选择结束日期" class="layui-input" lay-verify="required">
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始时间<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="startTime" lay-verify="required" lay-filter="startTime">
                          ${generateTimeOptions(schedule ? schedule.startTime : '', '', false)}
                        </select>
                      </td>
                      <td class="label-cell">结束时间<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="endTime" lay-verify="required">
                          ${generateTimeOptions(
														schedule ? schedule.endTime : '',
														schedule ? schedule.startTime : '',
														true
													)}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">参会省领导</td>
                      <td class="input-cell">
                        <input type="text" value="${
													schedule ? schedule.provinceLeader || '' : ''
												}" name="provinceLeader" placeholder="请输入参会省领导" class="layui-input">
                      </td>
											<td class="label-cell">参会地点<span class="required-mark">*</span></td>
                      <td class="input-cell" >
                        <input type="text" value="${
													schedule ? schedule.location || '' : ''
												}"  name="location" placeholder="请输入参会地点" lay-verify="required" class="layui-input">
                      </td>
                    </tr>
                    <tr>
											<td class="label-cell">会议类型</td>
                      <td class="input-cell">
                        <select name="meetingType">
                          ${generateSelectOptions(
														'meetingTypes',
														schedule ? schedule.meetingType || '' : ''
													)}
                        </select>
                      </td>
											<td class="label-cell">会议内容</td>
                      <td class="input-cell">
                        <textarea name="content" placeholder="请输入会议内容" class="layui-textarea" style="height: 60px; resize: vertical;">${
													schedule ? schedule.content || schedule.description || '' : ''
												}</textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">材料准备处室</td>
                      <td class="input-cell">
                        <select name="prepareDept">
                          ${generateSelectOptions(
														'departments',
														schedule ? schedule.prepareDept || '' : ''
													)}
                        </select>
                      </td>
                      <td class="label-cell">陪同参会处室</td>
                      <td class="input-cell">
                        <select name="accompanyDept">
                          ${generateSelectOptions(
														'departments',
														schedule ? schedule.accompanyDept || '' : ''
													)}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell textarea-cell">领导签批意见</td>
                      <td class="input-cell">
                        <textarea name="leaderOpinion" placeholder="请输入领导签批意见" class="layui-textarea" style="height: 80px; resize: vertical;">${
													schedule ? schedule.leaderOpinion || '' : ''
												}</textarea>
                      </td>
                      <td class="label-cell textarea-cell">材料短信提醒</td>
                      <td class="input-cell">
                        <input type="checkbox" name="smsRemind" title="启用短信提醒" lay-skin="primary" ${
													schedule && schedule.smsRemind ? 'checked' : ''
												}>
                        <textarea name="materialContent" placeholder="请输入材料短信内容" class="layui-textarea textarea-with-margin" style="height: 60px; resize: vertical;">${
													schedule ? schedule.materialContent || '' : ''
												}</textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">领导短信提醒</td>
                      <td colspan="3" class="input-cell">
                        <div class="radio-group">
                          <input type="radio" name="leaderSmsType" value="timely" title="及时" lay-filter="smsType" ${
														schedule && schedule.leaderSmsType === 'timely' ? 'checked' : ''
													}>
                          <input type="radio" name="leaderSmsType" value="scheduled" title="定时" lay-filter="smsType" ${
														schedule && schedule.leaderSmsType === 'scheduled' ? 'checked' : ''
													}>
                        </div>
                        <textarea name="leaderSmsContent" placeholder="请输入领导短信内容" class="layui-textarea" style="height: 80px; resize: vertical;">${
													schedule ? schedule.leaderSmsContent || '' : ''
												}</textarea>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </form>
            </div>

            <!-- 固定在底部的按钮区域 -->
            <div class="modal-buttons">
              <button type="button" class="modal-btn modal-btn-default" onclick="layer.closeAll()">取消</button>
              <button class="modal-btn modal-btn-primary" lay-submit lay-filter="submitSchedule">保存</button>
            </div>
          </div>
        `;

					const index = layer.open({
						type: 1,
						title: false,
						area: ['1000px', '720px'],
						content: content,
						shadeClose: false,
						resize: false,
						success: function () {
							// 如果是编辑模式，设置表单初始值
							if (isEdit && schedule) {
								// 设置表单值
								form.val('leader_schedule_form', {
									leader: schedule.leader || '',
									title: schedule.title || '',
									scheduleDate: schedule.scheduleDate || '',
									endDate: schedule.endDate || '',
									startTime: schedule.startTime || '',
									endTime: schedule.endTime || '',
									provinceLeader: schedule.provinceLeader || '',
									location: schedule.location || '',
									meetingType: schedule.meetingType || '',
									content: schedule.content || schedule.description || '',
									prepareDept: schedule.prepareDept || '',
									accompanyDept: schedule.accompanyDept || '',
									leaderOpinion: schedule.leaderOpinion || '',
									materialContent: schedule.materialContent || '',
									leaderSmsType: schedule.leaderSmsType || 'timely',
									leaderSmsContent: schedule.leaderSmsContent || '',
									smsRemind: schedule.smsRemind || false,
								});
							}

							form.render();

							// 初始化日期选择器
							layui.use('laydate', function () {
								const laydate = layui.laydate;

								// 开始日期选择器
								laydate.render({
									elem: 'input[name="scheduleDate"]',
									type: 'date',
									format: 'yyyy-MM-dd',
									done: function (value, date) {
										// 当开始日期改变时，更新结束日期的最小值
										const endDateInput = $('input[name="endDate"]');
										if (endDateInput.val() && value > endDateInput.val()) {
											endDateInput.val(value);
										}
									},
								});

								// 结束日期选择器
								laydate.render({
									elem: 'input[name="endDate"]',
									type: 'date',
									format: 'yyyy-MM-dd',
									done: function (value, date) {
										// 当结束日期改变时，检查是否早于开始日期
										const startDateInput = $('input[name="scheduleDate"]');
										if (startDateInput.val() && value < startDateInput.val()) {
											layer.msg('结束日期不能早于开始日期', { icon: 2 });
											$(this.elem).val(startDateInput.val());
										}
									},
								});
							});

							// 开始时间变化时更新结束时间选项
							form.on('select(startTime)', function (data) {
								updateEndTimeOptions(data.value);
							});

							// 更新结束时间选项
							function updateEndTimeOptions(startTime) {
								if (!startTime) return;

								const $endTimeSelect = $('select[name="endTime"]');
								const currentEndTime = $endTimeSelect.val();

								// 使用统一的函数生成结束时间选项
								const endTimeOptions = generateTimeOptions(currentEndTime, startTime, true);
								$endTimeSelect.html(endTimeOptions);

								// 重新渲染select
								form.render('select');
							}

							// 表单提交
							form.on('submit(submitSchedule)', function (data) {
								const formData = layui.form.val('leader_schedule_form');

								// 表单校验
								if (!validateScheduleForm(formData)) {
									return false;
								}

								// 调试：打印表单数据
								console.log('表单数据:', formData);

								if (isEdit) {
									updateSchedule(scheduleId, formData);
								} else {
									createSchedule(formData);
								}

								layer.close(index);
								return false;
							});

							// 表单校验函数
							function validateScheduleForm(formData) {
								// 检查必填字段
								if (
									!formData.leader ||
									!formData.title ||
									!formData.scheduleDate ||
									!formData.endDate ||
									!formData.startTime ||
									!formData.endTime ||
									!formData.location
								) {
									layer.msg('请填写所有必填字段！', { icon: 2 });
									return false;
								}

								// 检查日期逻辑
								if (formData.endDate < formData.scheduleDate) {
									layer.msg('结束日期不能早于开始日期', { icon: 2 });
									return false;
								}

								// 检查时间逻辑（同一天的情况）
								if (
									formData.scheduleDate === formData.endDate &&
									formData.endTime <= formData.startTime
								) {
									layer.msg('同一天的结束时间不能早于或等于开始时间', { icon: 2 });
									return false;
								}

								return true;
							}
						},
					});
				});
			}

			// 创建新日程
			function createSchedule(formData) {
				const newSchedule = {
					id: 'new_' + Date.now(),
					title: formData.title,
					leader: formData.leader,
					scheduleDate: formData.scheduleDate,
					endDate: formData.endDate,
					startTime: formData.startTime,
					endTime: formData.endTime,
					provinceLeader: formData.provinceLeader,
					location: formData.location,
					meetingType: formData.meetingType,
					content: formData.content,
					prepareDept: formData.prepareDept,
					accompanyDept: formData.accompanyDept,
					leaderOpinion: formData.leaderOpinion,
					materialContent: formData.materialContent,
					leaderSmsType: formData.leaderSmsType,
					leaderSmsContent: formData.leaderSmsContent,
					smsRemind: formData.smsRemind,
					type: formData.meetingType || 'meeting',
					status: 'pending',
					description: formData.content || '',
				};

				scheduleData[currentTab].push(newSchedule);
				refreshView();

				layui.use('layer', function () {
					const layer = layui.layer;
					layer.msg('日程创建成功！', { icon: 1 });
				});
			}

			// 更新日程
			function updateSchedule(scheduleId, formData) {
				const schedule = findScheduleById(scheduleId);
				if (schedule) {
					schedule.title = formData.title;
					schedule.leader = formData.leader;
					schedule.scheduleDate = formData.scheduleDate;
					schedule.endDate = formData.endDate;
					schedule.startTime = formData.startTime;
					schedule.endTime = formData.endTime;
					schedule.provinceLeader = formData.provinceLeader;
					schedule.location = formData.location;
					schedule.meetingType = formData.meetingType;
					schedule.content = formData.content;
					schedule.prepareDept = formData.prepareDept;
					schedule.accompanyDept = formData.accompanyDept;
					schedule.leaderOpinion = formData.leaderOpinion;
					schedule.materialContent = formData.materialContent;
					schedule.leaderSmsType = formData.leaderSmsType;
					schedule.leaderSmsContent = formData.leaderSmsContent;
					schedule.smsRemind = formData.smsRemind;
					schedule.type = formData.meetingType || 'meeting';
					schedule.description = formData.content || '';

					refreshView();

					layui.use('layer', function () {
						const layer = layui.layer;
						layer.msg('日程更新成功！', { icon: 1 });
					});
				}
			}

			// 编辑日程
			function editSchedule(scheduleId) {
				layui.use('layer', function () {
					const layer = layui.layer;
					layer.closeAll();
					showScheduleModal(null, scheduleId);
				});
			}

			// 从详情弹窗删除日程（删除成功后关闭详情弹窗）
			function deleteScheduleWithConfirm(scheduleId) {
				layui.use('layer', function () {
					const layer = layui.layer;

					layer.confirm('确定要删除这个日程吗？', { icon: 3, title: '提示' }, function (index) {
						const scheduleIndex = scheduleData[currentTab].findIndex(
							(item) => item.id === scheduleId
						);
						if (scheduleIndex > -1) {
							scheduleData[currentTab].splice(scheduleIndex, 1);
							refreshView();
							layer.msg('日程删除成功！', { icon: 1 });

							// 关闭详情弹窗
							if (window.currentDetailLayerIndex) {
								layer.close(window.currentDetailLayerIndex);
								window.currentDetailLayerIndex = null;
							}
						}
						layer.close(index);
					});
				});
			}

			// 更新月度日程数量显示
			function updateMonthScheduleCount() {
				const count = scheduleData[currentTab].length;
				const $countElement = $('#monthScheduleCount');
				if ($countElement.length) {
					$countElement.text(count);
				}
			}

			// 刷新视图
			function refreshView() {
				calendar.refetchEvents();
				renderScheduleList();
				updateMonthScheduleCount();
			}
		</script>
	</body>
</html>
