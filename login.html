<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1.0"
  >
  <title>登录</title>
  <link
    rel="stylesheet"
    href="layui/css/layui.css"
  >
  <link
    rel="stylesheet"
    href="style/login.css"
  >
  <script src="tools/jquery-3.7.1.min.js"></script>
</head>

<body>
  <div class="login-container">
    <i
      class="layui-icon layui-icon-cellphone qr-toggle"
      id="qrToggle"
      title="扫码登录"
    ></i>

    <div class="login-header">
      <h2>欢迎使用智慧财政</h2>
    </div>

    <div class="login-form">
      <div
        class="layui-tab login-tab"
        lay-filter="loginTab"
      >
        <ul class="layui-tab-title">
          <li class="layui-this">账号登录</li>
          <li>短信登录</li>
        </ul>
        <div class="layui-tab-content">
          <!-- 账号登录 -->
          <div class="layui-tab-item layui-show">
            <form
              class="layui-form"
              lay-filter="accountForm"
            >
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-username"></i>
                  </div>
                  <input
                    type="text"
                    name="username"
                    placeholder="请输入用户名"
                    class="layui-input login-input"
                    lay-verify="required"
                  >
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input
                    type="password"
                    name="password"
                    placeholder="请输入登录密码"
                    class="layui-input login-input"
                    lay-verify="required"
                    lay-affix="eye"
                  >
                </div>
              </div>
              <div class="login-tips">
                <a href="forget_password.html">忘记密码?</a>
              </div>
              <button
                class="layui-btn login-btn"
                lay-submit
                lay-filter="accountLogin"
              >登录</button>
            </form>
          </div>

          <!-- 短信登录 -->
          <div class="layui-tab-item">
            <form
              class="layui-form"
              lay-filter="smsForm"
            >
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-cellphone"></i>
                  </div>
                  <input
                    type="text"
                    name="phone"
                    placeholder="请输入手机号"
                    class="layui-input login-input"
                    lay-verify="required|phone"
                  >
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-vercode"></i>
                  </div>
                  <input
                    type="text"
                    name="smsCode"
                    placeholder="请输入验证码"
                    class="layui-input login-input"
                    lay-verify="required"
                  >
                  <button
                    type="button"
                    class="layui-btn layui-btn-primary"
                    id="smsBtn"
                    style="position:absolute; right:0; top:0; height:42px; border-radius:0 3px 3px 0;"
                  >获取验证码</button>
                </div>
                <button
                  class="layui-btn login-btn"
                  lay-submit
                  lay-filter="smsLogin"
                >登录</button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="login-footer">
      <p>推荐使用Chrome、Firefox、Edge等现代浏览器</p>
      <p>© 2025 湖南科创信息股份有限公司 版权所有</p>
    </div>
  </div>

  <!-- 二维码登录弹窗 -->
  <div
    class="qr-modal"
    id="qrModal"
  >
    <div class="qr-container">
      <i
        class="layui-icon layui-icon-close qr-close"
        id="qrClose"
      ></i>
      <div class="qr-title">手机扫码，安全登录</div>
      <div class="qr-placeholder">
        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
        <p>二维码生成中...</p>
      </div>
      <div class="qr-tips">
        <p>打开手机APP，扫描二维码即可登录</p>
        <p>扫码登录更安全，更便捷</p>
      </div>
    </div>
  </div>
  <script src="layui/layui.js"></script>

  <script>
    $(document).ready(function () {
      layui.use(['form', 'layer'], function () {
        var form = layui.form;
        var layer = layui.layer;

        // 短信验证码
        var smsCountdown = 0;
        function startSmsCountdown () {
          smsCountdown = 60;
          var $btn = $('#smsBtn').prop('disabled', true);

          var timer = setInterval(function () {
            $btn.text(--smsCountdown + 's后重新获取');
            if (smsCountdown <= 0) {
              clearInterval(timer);
              $btn.prop('disabled', false).text('获取验证码');
            }
          }, 1000);
        }

        $('#smsBtn').on('click', function () {
          var phone = $('input[name="phone"]').val();
          if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
            layer.msg(phone ? '请输入正确的手机号' : '请先输入手机号', { icon: 2 });
            return;
          }
          layer.msg('验证码已发送', { icon: 1 });
          startSmsCountdown();
        });

        // 二维码登录
        $('#qrToggle').on('click', function () {
          $('#qrModal').fadeIn(300);
          $('.qr-placeholder').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"  ></i><p>二维码生成中...</p>');
          // 生成二维码的过程
          setTimeout(function () {
            $('.qr-placeholder').html('<img src="./qr-code.png" alt="二维码" style="width:90%;height:90%;">');
          }, 1000);
        });

        $('#qrClose, .qr-modal').on('click', function (e) {
          if (e.target === this) {
            $('#qrModal').fadeOut(300);

          }
        });

        // 登录处理
        function handleLogin (field, isAccount) {
          var loadIndex = layer.load(1, { shade: [0.3, '#000'] });

          setTimeout(function () {
            layer.close(loadIndex);
            var success = isAccount ?
              (field.username === 'admin' && field.password === '123456') :
              (field.smsCode === '123456');

            if (success) {
              layer.msg('登录成功', { icon: 1 }, function () {
                window.location.href = 'home.html';
              });
            } else {
              layer.msg(isAccount ? '用户名或密码错误' : '验证码错误', { icon: 2 });
            }
          }, 1000);
        }

        // 表单提交
        form.on('submit(accountLogin)', function (data) {
          handleLogin(data.field, true);
          return false;
        });

        form.on('submit(smsLogin)', function (data) {
          handleLogin(data.field, false);
          return false;
        });

        // 手机号验证
        form.verify({
          phone: function (value) {
            if (!value) return '手机号不能为空';
            if (!/^1[3-9]\d{9}$/.test(value)) return '请输入正确的手机号';
          }
        });
      });

      // 键盘事件
      $(document).on('keydown', function (e) {
        if (e.keyCode === 27) $('#qrModal').fadeOut(300);
        if (e.keyCode === 13) {
          var $btn = $('.layui-tab-item.layui-show button[lay-submit]');
          if ($btn.length) $btn.click();
        }
      });
    });

  </script>
</body>

</html>