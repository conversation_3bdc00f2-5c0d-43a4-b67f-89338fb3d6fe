<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1.0"
  >
  <title>会议申请</title>
  <link
    rel="stylesheet"
    href="layui/css/layui.css"
  >
  <link
    rel="stylesheet"
    href="./style/metting-application.css"
  >
  <script src="./tools/jquery-3.7.1.min.js"></script>
  <!-- <style>
    /* 当前选中日期的样式 */
    .date-cell.current-day {
      background-color: #0c64eb;
      color: white;
      border-radius: 4px;
    }
  </style> -->
</head>

<body>
  <div class="meeting-container">
    <div class="calendar-header">
      <div class="header-left">
        <div
          class="schedule-count"
          id="scheduleCount"
        >本月会议0 场：</div>
      </div>

      <div class="header-center">
        <button
          class="nav-btn"
          id="prevWeekBtn"
        >
          <i class="layui-icon layui-icon-left"></i>
        </button>
        <input
          type="text"
          id="currentDate"
          class="layui-input current-date"
          placeholder="请选择年月"
          readonly
        >
        <button
          class="nav-btn"
          id="nextWeekBtn"
        >
          <i class="layui-icon layui-icon-right"></i>
        </button>
      </div>

      <div class="header-right">
        <div class="status-legend">
          <div class="legend-item">
            <div class="legend-color status-applying"></div>
            <span>申请中</span>
          </div>
          <div class="legend-item">
            <div class="legend-color status-occupied"></div>
            <span>已占用</span>
          </div>
          <div class="legend-item">
            <div class="legend-color status-mine"></div>
            <span>我的申请</span>
          </div>
        </div>

        <div class="action-buttons">
          <button
            class="new-booking-btn"
            id="newBookingBtn"
          >
            <i class="layui-icon layui-icon-add-1"></i>
            新建预定
          </button>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 左侧会议室列表 -->
      <div class="meeting-rooms">
        <div class="rooms-header">会议室</div>
        <div id="roomsList">
        </div>
      </div>

      <!-- 右侧日历区域 -->
      <div class="calendar-container">
        <div
          class="date-header"
          id="dateHeader"
        >
        </div>

        <!-- 时间段头部 -->
        <div
          class="time-header"
          id="timeHeader"
        >
        </div>

        <!-- 日历主体 -->
        <div
          class="calendar-body"
          id="calendarBody"
        >
        </div>
      </div>
    </div>
  </div>

  <script src="layui/layui.js"></script>
  <script>
    // ==================== 全局变量 ====================
    let currentDate = new Date();
    let currentRoom = 'room1';
    // ==================== 配置常量 ====================
    const CONFIG = {
      // 下拉框选项配置
      selectOptions: {
        leaders: [
          { value: '', text: '请选择参会领导' },
          { value: 'leader1', text: '张主任' },
          { value: 'leader2', text: '李副主任' },
          { value: 'leader3', text: '王处长' }
        ],
        approvers: [
          { value: '', text: '请选择审核人员' },
          { value: 'reviewer1', text: '审核人员1' },
          { value: 'reviewer2', text: '审核人员2' }
        ],
        timeSlots: [
          { value: '', text: '请选择时间段' },
          { value: 'morning', text: '上午' },
          { value: 'noon', text: '中午' },
          { value: 'afternoon', text: '下午' },
          { value: 'evening', text: '晚上' }
        ]
      },

      // 时间段顺序
      timeSlotOrder: ['morning', 'noon', 'afternoon', 'evening'],

      // 状态映射
      statusMap: {
        'available': '可用',
        'occupied': '已占用',
        'applying': '申请中',
        'mine': '我的申请'
      },

      // 状态颜色
      statusColors: {
        'available': '#f0f0f0',
        'occupied': '#ff6b6b',
        'applying': '#ffa726',
        'mine': '#42a5f5'
      }
    };
    //获取选项文本
    function getOptionText (optionType, value) {
      const options = CONFIG.selectOptions[optionType];
      if (!options) return value || '-';

      const option = options.find(opt => opt.value === value);
      return option ? option.text : (value || '-');
    }

    // 生成下拉框选项HTML

    function generateSelectOptions (optionType, selectedValue = '') {
      const options = CONFIG.selectOptions[optionType];
      if (!options) return '';

      return options.map(option => {
        const selected = selectedValue === option.value ? 'selected' : '';
        return `<option value="${option.value}" ${selected}>${option.text}</option>`;
      }).join('');
    }

    /**
     * 生成时间段选项（支持结束时间段过滤）
     * @param {string} selectedValue - 选中值
     * @param {string} startTimeSlot - 开始时间段
     * @param {boolean} isEndTime - 是否为结束时间段
     * @returns {string} HTML字符串
     */
    function generateTimeSlotOptions (selectedValue = '', startTimeSlot = '', isEndTime = false) {
      const placeholder = isEndTime ? '请选择结束时间段' : '请选择开始时间段';
      let html = `<option value="">${placeholder}</option>`;

      const startIndex = startTimeSlot ? CONFIG.timeSlotOrder.indexOf(startTimeSlot) : 0;
      // const availableSlots = isEndTime && startTimeSlot ?
      //   CONFIG.timeSlotOrder.slice(startIndex) :
      //   CONFIG.timeSlotOrder;
      const availableSlots = CONFIG.timeSlotOrder;
      html += availableSlots.map(timeSlot => {
        const option = CONFIG.selectOptions.timeSlots.find(opt => opt.value === timeSlot);
        if (option && option.value) {
          const selected = selectedValue === timeSlot ? 'selected' : '';
          return `<option value="${timeSlot}" ${selected}>${option.text}</option>`;
        }
        return '';
      }).join('');

      return html;
    }

    //格式化日期

    function formatDate (date, format = 'YYYY-MM-DD') {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      if (format === 'yyyy年MM月dd日') {
        return `${year}年${month}月${day}日`;
      }
      return `${year}-${month}-${day}`;
    }

    //获取状态文本

    function getStatusText (status) {
      return CONFIG.statusMap[status] || status;
    }

    //获取状态颜色
    function getStatusColor (status) {
      return CONFIG.statusColors[status] || '#666';
    }
    //会议室配置
    const rooms = [
      { id: 'room1', title: '会议室1', location: '综合楼2201', capacity: 20 },
      { id: 'room2', title: '会议室2', location: '综合楼2201', capacity: 20 },
      { id: 'room3', title: '会议室3', location: '综合楼2201', capacity: 20 },
      { id: 'room4', title: '会议室4', location: '综合楼2201', capacity: 20 },
      { id: 'room5', title: '会议室5', location: '综合楼2201', capacity: 20 },
      { id: 'room6', title: '会议室6', location: '综合楼2201', capacity: 20 },
      { id: 'room7', title: '会议室7', location: '综合楼2201', capacity: 20 },
      { id: 'room8', title: '会议室8', location: '综合楼2201', capacity: 20 }
    ];

    // 时间段配置

    const timeSlots = [
      { key: 'morning', label: '上午', start: '08:00', end: '12:00' },
      { key: 'noon', label: '中午', start: '12:00', end: '14:00' },
      { key: 'afternoon', label: '下午', start: '14:00', end: '18:00' },
      { key: 'evening', label: '晚上', start: '18:00', end: '22:00' }
    ];

    const meetingData = {
      'room1': {
        '2025-07-08': {
          'morning': {
            title: '客户会议',
            status: 'occupied',
            booker: '张三',
            department: '销售部',
            attendees: 10,
            remark: '重要客户洽谈',
            meetingDate: '2025-07-08',
            startTimeSlot: 'morning',
            endTimeSlot: 'morning',
            id: 'meeting_001'
          }
        },
        '2025-07-09': {
          'afternoon': {
            title: '产品评审',
            status: 'occupied',
            booker: '李四',
            department: '产品部',
            attendees: 12,
            remark: '新产品功能评审',
            meetingDate: '2025-07-09',
            startTimeSlot: 'afternoon',
            endTimeSlot: 'afternoon',
            id: 'meeting_002'
          }
        },
        '2025-07-10': {
          'morning': {
            title: '部门会议',
            status: 'occupied',
            booker: '王五',
            department: '技术部',
            attendees: 15,
            remark: '全天技术研讨会',
            meetingDate: '2025-07-10',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_003'
          },
          'noon': {
            title: '部门会议',
            status: 'occupied',
            booker: '王五',
            department: '技术部',
            attendees: 15,
            remark: '全天技术研讨会',
            meetingDate: '2025-07-10',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_003'
          },
          'afternoon': {
            title: '部门会议',
            status: 'occupied',
            booker: '王五',
            department: '技术部',
            attendees: 15,
            remark: '全天技术研讨会',
            meetingDate: '2025-07-10',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_003'
          },
          'evening': {
            title: '部门会议',
            status: 'occupied',
            booker: '王五',
            department: '技术部',
            attendees: 15,
            remark: '全天技术研讨会',
            meetingDate: '2025-07-10',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_003'
          }
        },
        '2025-07-11': {
          'afternoon': {
            title: '项目讨论',
            status: 'mine',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 8,
            remark: '我的项目讨论',
            meetingDate: '2025-07-11',
            startTimeSlot: 'afternoon',
            endTimeSlot: 'evening',
            id: 'meeting_004'
          },
          'evening': {
            title: '项目讨论',
            status: 'mine',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 8,
            remark: '我的项目讨论',
            meetingDate: '2025-07-11',
            startTimeSlot: 'afternoon',
            endTimeSlot: 'evening',
            id: 'meeting_004'
          }
        }
      },
      'room2': {
        '2025-07-08': {
          'noon': {
            title: '培训会议',
            status: 'occupied',
            booker: '赵六',
            department: '人事部',
            attendees: 20,
            remark: '新员工培训',
            meetingDate: '2025-07-08',
            startTimeSlot: 'noon',
            endTimeSlot: 'noon',
            id: 'meeting_005'
          }
        },
        '2025-07-12': {
          'morning': {
            title: '测试',
            status: 'applying',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 5,
            remark: '测试会议',
            meetingDate: '2025-07-12',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_006'
          },
          'noon': {
            title: '测试',
            status: 'applying',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 5,
            remark: '测试会议',
            meetingDate: '2025-07-12',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_006'
          },
          'afternoon': {
            title: '测试',
            status: 'applying',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 5,
            remark: '测试会议',
            meetingDate: '2025-07-12',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_006'
          },
          'evening': {
            title: '测试',
            status: 'applying',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 5,
            remark: '测试会议',
            meetingDate: '2025-07-12',
            startTimeSlot: 'morning',
            endTimeSlot: 'evening',
            id: 'meeting_006'
          }
        }
      },
      'room3': {
        '2025-06-24': {
          'afternoon': {
            title: '技术分享',
            status: 'occupied',
            booker: '技术总监',
            department: '技术部',
            attendees: 30,
            remark: '前端技术分享会'
          }
        },
        '2025-06-25': {
          'morning': {
            title: '季度总结',
            status: 'mine',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 15,
            remark: '我的季度工作总结'
          }
        },
        '2025-06-28': {
          'morning': {
            title: '技术培训',
            status: 'occupied',
            booker: '技术部',
            department: '技术部',
            attendees: 20,
            remark: '新技术培训'
          }
        }
      },
      'room4': {
        '2025-06-24': {
          'morning': {
            title: '面试安排',
            status: 'occupied',
            booker: 'HR主管',
            department: '人事部',
            attendees: 5,
            remark: '技术岗位面试'
          }
        }
      },
      'room5': {
        '2025-06-24': {
          'evening': {
            title: '董事会议',
            status: 'mine',
            booker: '测试账号1',
            department: '信息中心',
            attendees: 8,
            remark: '月度董事会议'
          }
        },
        '2025-06-27': {
          'afternoon': {
            title: '供应商会议',
            status: 'applying',
            booker: '采购部',
            department: '采购部',
            attendees: 12,
            remark: '供应商合作洽谈'
          }
        }
      },
      'room6': {
        '2025-06-24': {
          'afternoon': {
            title: '供应商会议',
            status: 'applying',
            booker: '采购部',
            department: '采购部',
            attendees: 12,
            remark: '供应商合作洽谈'
          }
        }
      },
      'room7': {
        '2025-06-24': {
          'morning': {
            title: '内部培训',
            status: 'applying',
            booker: '人事部',
            department: '人事部',
            attendees: 25,
            remark: '新员工培训'
          }
        },
        '2025-06-28': {
          'afternoon': {
            title: '年中总结',
            status: 'applying',
            booker: '总经理',
            department: '管理层',
            attendees: 30,
            remark: '年中工作总结会议'
          }
        }
      }
    };

    // 初始化日期选择器（只初始化一次）
    function initDatePicker () {
      layui.use('laydate', function () {
        const laydate = layui.laydate;

        // 渲染日期控件，点击弹出选择器
        laydate.render({
          elem: '#currentDate',
          type: 'date',
          format: 'yyyy年MM月dd日',
          value: currentDate,
          btns: ['now'],
          done: function (value, date) {
            // 日期选择完成后更新当前日期并刷新日历
            currentDate = new Date(date.year, date.month - 1, date.date);
            updateCurrentDateDisplay();
            initCalendar();
            updateScheduleCount();
          }
        });
      });
    }

    // 页面初始化
    $(document).ready(function () {
      generateRoomsList();
      initCalendar();
      bindEvents();
      updateScheduleCount();
      initDatePicker(); // 初始化日期选择器
    });

    // 生成会议室列表  
    function generateRoomsList () {
      const $roomsList = $('#roomsList');
      let html = '';
      $.each(rooms, function (index, room) {
        html += `
          <div class="room-item" data-room="${room.id}">
            <div class="room-name">${room.title}</div>
            <div class="room-info"><span>${room.location} </span><span>容纳：${room.capacity}</span></div>
          </div>
        `;
      });

      $roomsList.html(html);
    }

    // 初始化日历
    function initCalendar () {
      generateDateHeader();
      generateTimeHeader();
      generateCalendarBody();
    }

    // 生成日期头部  
    function generateDateHeader () {
      const $dateHeader = $('#dateHeader');
      let html = '';

      // 以当前日期为中心，显示前3天、当天、后3天
      for (let i = -3; i <= 3; i++) {
        const date = new Date(currentDate);
        date.setDate(date.getDate() + i);

        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const dayName = dayNames[date.getDay()];
        const dateNum = date.getDate();
        const month = date.getMonth() + 1;

        // 判断是否为当前选中日期
        const isToday = i === 0;
        const todayClass = isToday ? 'current-day' : '';

        html += `
          <div class="date-cell ${todayClass}">
            <div>${month}/${dateNum} ${dayName}</div>
          </div>
        `;
      }

      $dateHeader.html(html);

      // 更新顶部中间的当前日期显示
      updateCurrentDateDisplay();
    }

    // 更新当前日期显示
    function updateCurrentDateDisplay () {
      const $currentDateEl = $('#currentDate');
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const day = currentDate.getDate();

      // 只显示日期文本
      $currentDateEl.val(`${year}年${month}月${day}日`);
    }

    // 生成时间段头部  
    function generateTimeHeader () {
      const $timeHeader = $('#timeHeader');
      let html = '';

      for (let i = 0; i < 7; i++) {
        html += `
          <div class="time-group">
            <div class="time-slot-header">上午</div>
            <div class="time-slot-header">中午</div>
            <div class="time-slot-header">下午</div>
            <div class="time-slot-header">晚上</div>
          </div>
        `;
      }

      $timeHeader.html(html);
    }

    // 生成日历主体  
    function generateCalendarBody () {
      const $calendarBody = $('#calendarBody');
      let html = '';

      $.each(rooms, function (index, room) {
        html += `<div class="room-row" data-room="${room.id}">`;

        for (let dayIndex = -3; dayIndex <= 3; dayIndex++) {
          const date = new Date(currentDate);
          date.setDate(date.getDate() + dayIndex);
          const dateStr = formatDate(date);

          html += `<div class="day-column">`;

          $.each(timeSlots, function (index, timeSlot) {
            const meeting = getMeetingForSlot(room.id, dateStr, timeSlot.key);
            const eventHtml = meeting ?
              `<div class="event ${meeting.status}" onclick="event.stopPropagation(); showEventDetail('${room.id}', '${dateStr}', '${timeSlot.key}')" title="${meeting.title}">${meeting.title.length > 4 ? meeting.title.substring(0, 4) : meeting.title}</div>` : '';

            html += `
              <div class="time-slot"
                   data-room="${room.id}"
                   data-date="${dateStr}"
                   data-time="${timeSlot.key}"
                   onclick="handleTimeSlotClick('${room.id}', '${dateStr}', '${timeSlot.key}')">
                ${eventHtml}
              </div>
            `;
          });

          html += `</div>`;
        }

        html += `</div>`;
      });

      $calendarBody.html(html);
    }

    // 获取指定时间段的会议
    function getMeetingForSlot (roomId, date, timeSlot) {
      return meetingData[roomId] && meetingData[roomId][date] && meetingData[roomId][date][timeSlot] || null;
    }
    // 处理时间段点击
    function handleTimeSlotClick (roomId, date, timeSlot) {
      const meeting = getMeetingForSlot(roomId, date, timeSlot);
      if (meeting) {
        showEventDetail(roomId, date, timeSlot);
      } else {
        showBookingModal(roomId, date, timeSlot);
      }
    }

    // 更新会议统计 
    function updateScheduleCount () {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      let monthlyCount = 0;

      // 统计当前月份的会议数量
      $.each(meetingData, function (roomId, roomData) {
        $.each(roomData, function (date, dateData) {
          const eventDate = new Date(date);
          if (eventDate.getMonth() === currentMonth && eventDate.getFullYear() === currentYear) {
            // 统计当天的会议数量（去重相同ID的会议）
            const uniqueMeetings = new Set();
            $.each(dateData, function (timeSlot, meeting) {
              if (meeting && meeting.id) {
                uniqueMeetings.add(meeting.id);
              } else {
                // 如果没有ID，按时间段计算
                uniqueMeetings.add(`${date}_${timeSlot}`);
              }
            });
            monthlyCount += uniqueMeetings.size;
          }
        });
      });

      $('#scheduleCount').text(`本月会议：${monthlyCount} 场`);
    }

    // 绑定事件  
    function bindEvents () {
      // 新建预定按钮
      $('#newBookingBtn').on('click', function () {
        showBookingModal();
      });

      // 上一天按钮
      $('#prevWeekBtn').on('click', function () {
        currentDate.setDate(currentDate.getDate() - 1);
        initCalendar();
        updateScheduleCount();
      });

      // 下一天按钮
      $('#nextWeekBtn').on('click', function () {
        currentDate.setDate(currentDate.getDate() + 1);
        initCalendar();
        updateScheduleCount();
      });

      // 会议室点击事件 - 使用事件委托
      $('#roomsList').on('click', '.room-item', function () {
        // 移除其他房间的active状态
        $('.room-item').removeClass('active');
        // 添加当前房间的active状态
        $(this).addClass('active');

        currentRoom = $(this).data('room');
        highlightRoomRow(currentRoom);
      });
    }

    // 高亮会议室行  
    function highlightRoomRow (roomId) {
      // 移除所有高亮
      $('.room-row').removeClass('highlighted');
      // 高亮指定会议室行
      $(`.room-row[data-room="${roomId}"]`).addClass('highlighted');
    }


    /**
     * 检查单天会议时间段冲突
     * @param {string} roomId - 会议室ID
     * @param {string} meetingDate - 会议日期
     * @param {string} startTimeSlot - 开始时间段
     * @param {string} endTimeSlot - 结束时间段
     * @returns {Object} 冲突检查结果
     */
    function checkTimeSlotConflict (roomId, meetingDate, startTimeSlot, endTimeSlot) {
      if (!meetingData[roomId] || !meetingData[roomId][meetingDate]) {
        return { hasConflict: false };
      }

      const newStartIndex = CONFIG.timeSlotOrder.indexOf(startTimeSlot);
      const newEndIndex = CONFIG.timeSlotOrder.indexOf(endTimeSlot);

      for (let i = newStartIndex; i <= newEndIndex; i++) {
        const timeSlot = CONFIG.timeSlotOrder[i];
        const existingMeeting = meetingData[roomId][meetingDate][timeSlot];

        if (existingMeeting && existingMeeting.status !== 'available') {
          return {
            hasConflict: true,
            conflictMeeting: existingMeeting.title || '已有会议',
            conflictTimeSlot: timeSlot
          };
        }
      }

      return { hasConflict: false };
    }

    /**
     * 检查跨天会议时间段冲突
     * @param {string} roomId - 会议室ID
     * @param {string} startDate - 开始日期
     * @param {string} startTimeSlot - 开始时间段
     * @param {string} endDate - 结束日期
     * @param {string} endTimeSlot - 结束时间段
     * @returns {Object} 冲突检查结果
     */
    function checkMultiDayTimeSlotConflict (roomId, startDate, startTimeSlot, endDate, endTimeSlot) {
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      const currentDate = new Date(startDateObj);
      while (currentDate <= endDateObj) {
        const dateStr = formatDate(currentDate);
        let dayStartIndex, dayEndIndex;

        // 确定当天需要检查的时间段范围
        if (dateStr === startDate && dateStr === endDate) {
          dayStartIndex = CONFIG.timeSlotOrder.indexOf(startTimeSlot);
          dayEndIndex = CONFIG.timeSlotOrder.indexOf(endTimeSlot);
        } else if (dateStr === startDate) {
          dayStartIndex = CONFIG.timeSlotOrder.indexOf(startTimeSlot);
          dayEndIndex = CONFIG.timeSlotOrder.length - 1;
        } else if (dateStr === endDate) {
          dayStartIndex = 0;
          dayEndIndex = CONFIG.timeSlotOrder.indexOf(endTimeSlot);
        } else {
          dayStartIndex = 0;
          dayEndIndex = CONFIG.timeSlotOrder.length - 1;
        }

        const dayConflict = checkTimeSlotConflict(
          roomId,
          dateStr,
          CONFIG.timeSlotOrder[dayStartIndex],
          CONFIG.timeSlotOrder[dayEndIndex]
        );

        if (dayConflict.hasConflict) {
          return {
            hasConflict: true,
            conflictDate: dateStr,
            conflictMeeting: dayConflict.conflictMeeting,
            conflictTimeSlot: dayConflict.conflictTimeSlot
          };
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return { hasConflict: false };
    }

    // 将新会议添加到日历数据结构中（支持跨天会议）
    function addMeetingToCalendarData (meetingInfo) {
      console.log(`output->meetingInfo`, meetingInfo)
      const roomId = meetingInfo.room;

      const startDateObj = new Date(meetingInfo.startDate);
      const endDateObj = new Date(meetingInfo.endDate);

      // 遍历每一天
      const currentDate = new Date(startDateObj);
      while (currentDate <= endDateObj) {
        const dateStr = formatDate(currentDate);

        // 确定当天需要添加的时间段范围
        let dayStartIndex, dayEndIndex;

        if (dateStr === meetingInfo.startDate && dateStr === meetingInfo.endDate) {
          // 同一天：使用指定的时间段范围
          dayStartIndex = CONFIG.timeSlotOrder.indexOf(meetingInfo.startTimeSlot);
          dayEndIndex = CONFIG.timeSlotOrder.indexOf(meetingInfo.endTimeSlot);
        } else if (dateStr === meetingInfo.startDate) {
          // 开始日期：从开始时间段到晚上
          dayStartIndex = CONFIG.timeSlotOrder.indexOf(meetingInfo.startTimeSlot);
          dayEndIndex = CONFIG.timeSlotOrder.length - 1;
        } else if (dateStr === meetingInfo.endDate) {
          // 结束日期：从早上到结束时间段
          dayStartIndex = 0;
          dayEndIndex = CONFIG.timeSlotOrder.indexOf(meetingInfo.endTimeSlot);
        } else {
          // 中间日期：全天
          dayStartIndex = 0;
          dayEndIndex = CONFIG.timeSlotOrder.length - 1;
        }

        // 初始化数据结构
        if (!meetingData[roomId]) {
          meetingData[roomId] = {};
        }
        if (!meetingData[roomId][dateStr]) {
          meetingData[roomId][dateStr] = {};
        }

        // 为当天的每个时间段添加会议数据
        for (let i = dayStartIndex; i <= dayEndIndex; i++) {
          const timeSlot = CONFIG.timeSlotOrder[i];
          meetingData[roomId][dateStr][timeSlot] = meetingInfo;
        }

        // 移动到下一天
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    // 显示事件详情
    function showEventDetail (roomId, date, timeSlot) {
      const meeting = getMeetingForSlot(roomId, date, timeSlot);
      if (!meeting) return;

      const room = rooms.find(r => r.id === roomId);
      const timeSlotInfo = timeSlots.find(t => t.key === timeSlot);

      layui.use('layer', function () {
        const layer = layui.layer;

        const content = `
          <div class="modal-container">
            <!-- 标题栏 -->
            <div class="modal-header">
              <h3>会议详情</h3>
            </div>

            <!-- 表单内容 -->
            <div class="modal-content">
              <div class="modal-form-container">
                <table class="layui-table modal-table">
                  <tbody>
                    <tr>
                      <td class="label-cell">会议室</td>
                      <td class="content-cell">
                        <span>${room ? room.title : '未知会议室'}</span>
                      </td>
                      <td class="label-cell">预定人</td>
                      <td class="content-cell">
                        <span>${meeting.booker || '-'}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">预定处室</td>
                      <td class="content-cell">
                        <span>${meeting.department || '-'}</span>
                      </td>
                      <td class="label-cell">会议名称</td>
                      <td class="content-cell">
                        <span class="highlight">${meeting.title}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始日期</td>
                      <td class="content-cell">
                        <span>${meeting.startDate || meeting.meetingDate || date}</span>
                      </td>
                      <td class="label-cell">开始时间段</td>
                      <td class="content-cell">
                        <span>${getOptionText('timeSlots', meeting.startTimeSlot || timeSlot)}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">结束日期</td>
                      <td class="content-cell">
                        <span>${meeting.endDate || meeting.meetingDate || date}</span>
                      </td>
                      <td class="label-cell">结束时间段</td>
                      <td class="content-cell">
                        <span>${getOptionText('timeSlots', meeting.endTimeSlot || timeSlot)}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">状态</td>
                      <td class="content-cell">
                        <span style="background: ${getStatusColor(meeting.status)}; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px;">${getStatusText(meeting.status)}</span>
                      </td>
                      <td class="label-cell"></td>
                      <td class="content-cell"></td>
                    </tr>

                    <tr>
                      <td class="label-cell">参会领导</td>
                      <td class="content-cell">
                        <span>${meeting.leader ? getOptionText('leaders', meeting.leader) : '-'}</span>
                      </td>
                      <td class="label-cell">参会处室</td>
                      <td class="content-cell">
                        <span>${meeting.attendDept || '-'}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">外部单位</td>
                      <td class="content-cell">
                        <span>${meeting.externalUnit || '-'}</span>
                      </td>
                      <td class="label-cell">外部领导</td>
                      <td class="content-cell">
                        <span>${meeting.externalLeader || '-'}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">参会人数</td>
                      <td class="content-cell">
                        <span>${meeting.attendees || 0}人</span>
                      </td>
                      <td class="label-cell">审核人员</td>
                      <td class="content-cell">
                        <span>${meeting.approver ? getOptionText('approvers', meeting.approver) : '-'}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">会议室资源</td>
                      <td class="content-cell">
                        <span>${meeting.videoEquip ? '需要视频设备' : '无特殊设备需求'}</span>
                      </td>
                      <td class="label-cell vertical-top">备注</td>
                      <td class="content-cell">
                        <span>${meeting.remark || '无备注信息'}</span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>

            <!-- 底部按钮 -->
            <div class="modal-buttons">
              ${(meeting.booker === '测试账号1' || meeting.status === 'applying') ?
            `<button type="button" class="modal-btn modal-btn-danger delete-meeting-btn" data-room="${roomId}" data-date="${date}" data-time="${timeSlot}">删除会议</button>` :
            ''
          }
              <button type="button" class="modal-btn modal-btn-default close-detail-btn">关闭</button>
            </div>
          </div>
        `;

        const detailIndex = layer.open({
          type: 1,
          title: false,
          area: ['840px', '600px'],
          content: content,
          shadeClose: false,
          success: function (layero, index) {
            // 绑定关闭按钮事件
            layero.find('.close-detail-btn').on('click', function () {
              layer.close(index);
            });

            // 绑定删除按钮事件
            layero.find('.delete-meeting-btn').on('click', function () {
              const roomId = $(this).data('room');
              const date = $(this).data('date');
              const timeSlot = $(this).data('time');
              deleteEvent(roomId, date, timeSlot, index);
            });
          }
        });
      });
    }

    // 删除事件
    function deleteEvent (roomId, date, timeSlot, detailIndex) {
      layui.use('layer', function () {
        const layer = layui.layer;

        layer.confirm('确定要删除这个会议吗？', {
          icon: 3,
          title: '提示'
        }, function (confirmIndex) {
          // 获取要删除的会议信息
          const meetingToDelete = meetingData[roomId] && meetingData[roomId][date] && meetingData[roomId][date][timeSlot];

          if (meetingToDelete && meetingToDelete.id) {
            const meetingId = meetingToDelete.id;

            // 删除所有相同ID的会议（跨时间段会议的所有部分）
            Object.keys(meetingData).forEach(rId => {
              Object.keys(meetingData[rId]).forEach(d => {
                Object.keys(meetingData[rId][d]).forEach(ts => {
                  if (meetingData[rId][d][ts] && meetingData[rId][d][ts].id === meetingId) {
                    delete meetingData[rId][d][ts];
                  }
                });
              });
            });

            // 同时从meetingList中删除
            if (window.meetingList) {
              window.meetingList = window.meetingList.filter(meeting => meeting.id !== meetingId);
            }
          } else {
            // 如果没有ID，只删除当前时间段
            if (meetingData[roomId] && meetingData[roomId][date] && meetingData[roomId][date][timeSlot]) {
              delete meetingData[roomId][date][timeSlot];
            }
          }

          // 刷新日历显示
          generateCalendarBody();
          updateScheduleCount();

          // 关闭确认弹窗
          layer.close(confirmIndex);

          // 关闭详情弹窗
          if (detailIndex) {
            layer.close(detailIndex);
          }

          // 显示成功消息
          layer.msg('会议已删除', { icon: 1 });
        });
      });
    }





    // 更新结束时间段选项  
    function updateEndTimeSlotOptions (startTimeSlot, layero) {
      if (!startTimeSlot) return;

      const $endTimeSlotSelect = layero.find('select[name="endTimeSlot"]');
      const currentValue = $endTimeSlotSelect.val();

      // 使用统一的函数生成结束时间段选项
      const endTimeSlotOptions = generateTimeSlotOptions(currentValue, startTimeSlot, true);
      $endTimeSlotSelect.html(endTimeSlotOptions);

      // 重新渲染select
      layui.form.render('select');
    }

    // 显示会议预定弹窗
    function showBookingModal (roomId = null, date = null, timeSlot = null) {
      layui.use(['layer', 'form'], function () {
        const layer = layui.layer;
        const form = layui.form;

        const selectedRoom = roomId || rooms[0].id;
        const selectedDate = date || formatDate(new Date());
        const selectedTimeSlot = timeSlot || 'morning';

        // 判断是否从日历空白区域点击进入（有roomId、date、timeSlot参数）
        const isFromCalendar = roomId && date && timeSlot;
        const roomDisabled = isFromCalendar ? 'disabled' : '';

        const content = `
          <div class="modal-container">
            <!-- 标题栏 -->
            <div class="modal-header" style="position: relative;">
              <h3>会议室预定</h3>
              <div class="booking-tips">
                <div>如需紧急水请联系物业: 何玉凤 13812345498</div>
                <div>如需信息设备请联系: 何玉凤 13812345498</div>
              </div>
            </div>

            <!-- 表单内容 -->
            <div class="modal-content">
              <form class="layui-form modal-form-container" lay-filter="bookingForm" id="bookingForm">
                <table class="layui-table modal-table">
                  <tbody>
                    <tr>
                      <td class="label-cell">会议室 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="room" lay-verify="required" ${roomDisabled}>
                          ${rooms.map(room => `<option value="${room.id}" ${selectedRoom === room.id ? 'selected' : ''}>${room.title}</option>`).join('')}
                        </select>
                      </td>
                      <td class="label-cell">预定人 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="booker" value="测试账号1" lay-verify="required" class="layui-input" autocomplete="off" disabled>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">预定处室 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="department" value="信息中心" lay-verify="required" class="layui-input" autocomplete="off" disabled>
                      </td>
                      <td class="label-cell">会议名称 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="title" lay-verify="required" placeholder="请输入会议名称" class="layui-input" autocomplete="off">
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">开始日期 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="startDate" id="startDate" lay-verify="required" class="layui-input" placeholder="请选择开始日期" readonly ${isFromCalendar ? 'disabled' : ''}>
                      </td>
                      <td class="label-cell">开始时间段 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="startTimeSlot" lay-verify="required" lay-filter="startTimeSlot" ${isFromCalendar ? 'disabled' : ''}>
                          ${generateTimeSlotOptions(selectedTimeSlot, '', false)}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">结束日期 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="text" name="endDate" id="endDate" lay-verify="required" class="layui-input" placeholder="请选择结束日期" readonly>
                      </td>
                      <td class="label-cell">结束时间段 <span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <select name="endTimeSlot" lay-verify="required">
                          ${generateTimeSlotOptions('', selectedTimeSlot, true)}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">参会人数<span class="required-mark">*</span></td>
                      <td class="input-cell">
                        <input type="number"  lay-verify="required" name="attendees" placeholder="请输入参会人数" class="layui-input" autocomplete="off">
                      </td>
                      <td class="label-cell"></td>
                      <td class="input-cell"></td>
                    </tr>
                    <tr>
                      <td class="label-cell">参会领导</td>
                      <td class="input-cell">
                        <select name="leader">
                          ${generateSelectOptions('leaders')}
                        </select>
                      </td>
                      <td class="label-cell">参会处室</td>
                      <td class="input-cell">
                        <input type="text" name="attendDept"  placeholder="请输入参会处室" class="layui-input" autocomplete="off">
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">外部单位</td>
                      <td class="input-cell">
                        <input type="text" name="externalUnit" placeholder="请输入外部单位" class="layui-input" autocomplete="off">
                      </td>
                      <td class="label-cell">外部领导</td>
                      <td class="input-cell">
                        <input type="text" name="externalLeader" placeholder="请输入外部单位领导" class="layui-input" autocomplete="off">
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell">审核人员</td>
                      <td class="input-cell">
                        <select name="approver">
                          ${generateSelectOptions('approvers')}
                        </select>
                      </td>
                      <td class="label-cell">会议室资源</td>
                      <td class="input-cell">
                        <input type="checkbox" name="videoEquip" lay-skin="primary" title="视频设备" value="1">
                      </td>
                    </tr>
                    <tr>

                      <td class="label-cell">附件上传</td>
                      <td class="input-cell" colspan="3">
                        <div class="layui-upload">
                          <div style="display:flex;align-items:center;gap:20px"><button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="uploadBtn">选择文件</button> <span style="font-size:13px;color:#888888">提示：只能上传docx,xls,pdf文件，且单个文件不超过2mb</span></div>
                          <div class="layui-upload-list">
                            <table class="layui-table upload-table">
                              <thead>
                                <tr><th>文件名</th><th>大小</th><th>状态</th><th>操作</th></tr>
                              </thead>
                              <tbody id="uploadList"></tbody>
                            </table>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="label-cell textarea-cell">备注</td>
                      <td colspan="3" class="input-cell">
                        <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea" style="height: 80px; resize: vertical;"></textarea>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </form>
            </div>

            <!-- 底部按钮 -->
            <div class="modal-buttons">
              <button type="button" class="cancel-btn">取消</button>
              <button type="submit" lay-submit lay-filter="bookingSubmit" class="submit-btn">确定</button>
            </div>
          </div>
        `;

        const bookingIndex = layer.open({
          type: 1,
          title: false,
          area: ['1000px', '720px'],
          content: content,
          shadeClose: false,
          resize: false,
          success: function (layero, index) {
            // 先渲染表单
            form.render();

            // 初始化layui控件
            layui.use(['laydate', 'form', 'upload'], function () {
              const laydate = layui.laydate;
              const upload = layui.upload;

              // 初始化开始日期控件
              laydate.render({
                elem: '#startDate',
                type: 'date',
                format: 'yyyy-MM-dd',
                value: selectedDate,
                min: formatDate(new Date()), // 最小日期为今天
                done: function (value, date) {
                  // 开始日期改变时，更新结束日期的最小值
                  laydate.render({
                    elem: '#endDate',
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    value: layero.find('#endDate').val() || value,
                    min: value // 结束日期不能早于开始日期
                  });
                }
              });

              // 初始化结束日期控件
              laydate.render({
                elem: '#endDate',
                type: 'date',
                format: 'yyyy-MM-dd',
                value: selectedDate,
                min: selectedDate // 初始最小日期为选中日期
              });

              // 初始化文件上传控件
              var uploadListIns = upload.render({
                elem: '#uploadBtn',
                url: '/upload/', // 上传接口地址
                accept: 'file',
                acceptMime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf',
                multiple: true,
                auto: false, // 不自动上传
                bindAction: '#uploadBtn',
                choose: function (obj) {
                  const files = this.files = obj.pushFile();
                  obj.preview(function (index, file, result) {
                    const tr = $('<tr id="upload-' + index + '">' +
                      '<td>' + file.name + '</td>' +
                      '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>' +
                      '<td>等待上传</td>' +
                      '<td><button class="layui-btn layui-btn-xs layui-btn-danger upload-delete">删除</button></td>' +
                      '</tr>');
                    $('#uploadList').append(tr);
                    // 监听删除
                    tr.find('.upload-delete').on('click', function () {
                      delete files[index]; // 删除对应的文件
                      tr.remove(); // 删除表格行
                      // 清空 input file 值，以免删除后出现同名文件不可选
                      uploadListIns.config.elem.next()[0].value = '';
                    });
                  });
                },
                done: function (res, index, upload) {
                  if (res.code == 0) {
                    $('#upload-' + index).find('td').eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                  } else {
                    $('#upload-' + index).find('td').eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                  }
                }
              });
            });

            // 绑定取消按钮事件
            layero.find('.cancel-btn').on('click', function () {
              layer.close(index);
            });



            // 绑定开始时间段变化事件
            form.on('select(startTimeSlot)', function (data) {
              updateEndTimeSlotOptions(data.value, layero);
            });

            // 初始化结束时间段选项
            const initialStartTimeSlot = layero.find('select[name="startTimeSlot"]').val();
            if (initialStartTimeSlot) {
              updateEndTimeSlotOptions(initialStartTimeSlot, layero);
            }

            // 表单提交事件 - 确保在表单渲染后绑定 (因为这里按钮在layuiform之外 所以用 layui.form.val('id')来动态获取表单值)
            form.on('submit(bookingSubmit)', function (data) {
              console.log('=== 表单提交开始 ===');
              const formData = layui.form.val('bookingForm');
              console.log('表单数据:', formData);

              // 验证必填字段 预定人和预定处室是取值的不需要输入
              if (!formData.room || !formData.title || !formData.startDate || !formData.endDate || !formData.startTimeSlot || !formData.endTimeSlot || !formData.attendees) {
                layer.msg('请填写所有必填字段！', { icon: 2 });
                return false;
              }

              // 日期验证
              const startDate = new Date(formData.startDate);
              const endDate = new Date(formData.endDate);

              if (endDate < startDate) {
                layer.msg('结束日期不能早于开始日期！', { icon: 2 });
                return false;
              }

              // 时间段验证（同一天时才需要验证时间段顺序）
              const startIndex = CONFIG.timeSlotOrder.indexOf(formData.startTimeSlot);
              const endIndex = CONFIG.timeSlotOrder.indexOf(formData.endTimeSlot);

              if (formData.startDate === formData.endDate && endIndex < startIndex) {
                layer.msg('同一天内结束时间段不能早于开始时间段！', { icon: 2 });
                return false;
              }

              // 检查跨天时间冲突
              const conflictCheck = checkMultiDayTimeSlotConflict(formData.room, formData.startDate, formData.startTimeSlot, formData.endDate, formData.endTimeSlot);
              if (conflictCheck.hasConflict) {
                layer.msg(`时间段与现有会议冲突！冲突日期：${conflictCheck.conflictDate}，冲突会议：${conflictCheck.conflictMeeting}`, { icon: 2 });
                return false;
              }

              // 保存会议数据 - 支持跨时间段
              const meetingInfo = {
                title: (formData.title || '').trim(),
                status: 'applying',
                booker: (formData.booker || '').trim(),
                department: (formData.department || '').trim(),
                attendees: parseInt(formData.attendees) || 0,
                leader: formData.leader ? formData.leader.trim() : '',
                attendDept: formData.attendDept ? formData.attendDept.trim() : '',
                externalUnit: formData.externalUnit ? formData.externalUnit.trim() : '',
                externalLeader: formData.externalLeader ? formData.externalLeader.trim() : '',
                videoEquip: formData.videoEquip === '1' || formData.videoEquip === 'on',
                approver: formData.approver || '',
                remark: formData.remark ? formData.remark.trim() : '',
                startDate: formData.startDate,
                endDate: formData.endDate,
                startTimeSlot: formData.startTimeSlot,
                endTimeSlot: formData.endTimeSlot,
                // 保持兼容性
                meetingDate: formData.startDate,
                room: formData.room,
                createTime: new Date().toISOString(),
                id: 'meeting_' + Date.now() // 添加唯一ID
              };

              // 将会议添加到新的数据结构中
              if (!window.meetingList) {
                window.meetingList = [];
              }
              window.meetingList.push(meetingInfo);

              // 同时保持原有数据结构的兼容性（用于日历显示）
              addMeetingToCalendarData(meetingInfo);

              console.log('保存的会议数据:', meetingInfo);

              // 刷新日历显示
              generateCalendarBody();
              updateScheduleCount();

              // 关闭弹窗并提示成功
              layer.close(index);
              layer.msg('会议预定申请已提交！', { icon: 1 });
              return false;
            });
          }
        });
      });
    }
  </script>
</body>

</html>