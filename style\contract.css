* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f6f8fa;
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  overflow: hidden;
}

:root {
  --color-primary: #0000ff;
  --bg-color-active1: #dadff4;
  --bg-color-active2: #e0e9fa;
  --bg-color-active3: #eaf2ff;
}

/* 联系卡片统一样式 */
.personnel-list,
.mygroup-body,
.mygroup-detail-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.personnel-item,
.frequent-contact-item,
.group-item,
.group-member-item {
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.personnel-avatar,
.contact-avatar,
.group-avatar,
.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin-right: 16px;
  flex-shrink: 0;
}

.personnel-name,
.contact-name,
.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.personnel-position,
.contact-position,
.member-position {
  font-size: 13px;
  border-radius: 8px;
  background-color: #ebeff9;
  color: var(--color-primary);
  padding: 4px 8px;
  font-weight: 500;
}

.personnel-item:hover,
.frequent-contact-item:hover,
.group-item:hover,
.group-member-item:hover {
  background: var(--bg-color-active3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}



/* 通讯录容器 */

.contract-container {
  display: flex;
  height: 100vh;
  background: #fff;
}

/* 侧边栏 */
.sidebar {
  width: 225px;
  background: #f4f5f7;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 24px 20px 8px 28px;
}

.sidebar-header h3 {
  font-size: 24px;
  font-weight: 700;
  letter-spacing: 1px;
}

.sidebar-menu {
  flex: 1;
  padding: 12px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 16px;
  color: #555555;
  border-radius: 8px;
  margin: 0 8px 8px 8px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.menu-item:last-child i {
  font-size: 16px;
}

.menu-item i {
  font-size: 20px;
  margin-right: 10px;
  color: var(--color-primary);
}

.menu-item.active,
.menu-item:hover {
  background: var(--bg-color-active1);
  color: var(--color-primary);
}


/* 部门面板 */
.department-panel {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.department-list {
  flex: 1;
  overflow-y: auto;
}

.department-header {
  padding: 17px 24px;
  font-size: 20px;
  color: #333333;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
}

.department-item {
  padding: 14px 20px 14px 24px;
  font-size: 15px;
  color: #333;
  margin: 4px 8px;
  /* border-bottom: 1px solid #f0f1f3; */
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.2s, color 0.2s;
  transition: all .5s;
}

.department-item.active,
.department-item:hover {
  background: var(--bg-color-active2);
  color: var(--color-primary);
  font-weight: 600;
}

.department-item:hover {
  transform: translateX(4px);
}


/* 人员面板 */
.personnel-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.panel-header {
  padding: 18px 24px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
}

.breadcrumb {
  font-size: 18px;
  color: var(--color-primary);
}

.personnel-count {
  font-size: 13px;
  color: #b0b3b8;
  margin-left: 8px;
}



.personnel-avatar>i {
  font-size: 20px;
}

.personnel-avatar .avatar-text {
  font-size: 13px;
  font-weight: 700;
  letter-spacing: 1px;
}

.personnel-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
}


/* 详情弹窗 */
.personnel-detail {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  min-width: 340px;
}

.detail-header {
  display: flex;
  align-items: center;
  padding: 32px 32px 18px 32px;
  border-bottom: 1px solid #f0f1f3;
  position: relative;
}

.detail-avatar {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin-right: 24px;
  letter-spacing: 1px;
}

.detail-name {
  font-size: 22px;
  font-weight: 600;
  color: #222;
}

.detail-favorite {
  position: absolute;
  top: 32px;
  right: 32px;
  color: #b0b3b8;
  font-size: 22px;
  cursor: pointer;
  transition: color 0.2s;
}

.detail-favorite:hover {
  color: #ffd04b;
}

.detail-info {
  padding: 16px 32px 0px 32px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  /* border-bottom: 1px solid #f0f1f3; */
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 64px;
  font-size: 15px;
  color: #b0b3b8;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 15px;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  word-break: break-all;
}

.info-value i {
  color: var(--color-primary);
  font-size: 18px;
  margin-left: 6px;
  cursor: pointer;
}

.detail-actions {
  padding: 18px 32px 32px 32px;
}

.detail-actions .layui-btn {
  width: 100%;
  height: 48px;
  line-height: 48px;
  font-size: 18px;
  border-radius: 12px;
  background: #278dfe;
  color: #fff;
  font-weight: 700;
  border: none;
  transition: background 0.2s;
}

.detail-actions .layui-btn:hover {
  background: #52a4fe;
}

/* 弹窗层样式 */
.personnel-detail-layer .layui-layer-content {
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  background: #fff;
}


/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #b0b3b8;
  text-align: center;
}

.empty-state i {
  font-size: 54px;
  margin-bottom: 18px;
  color: #c0c4cc;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.empty-state p:first-of-type {
  font-size: 18px;
  color: #606266;
  margin-bottom: 8px;
}

/* 滚动条美化 */
.department-list::-webkit-scrollbar,
.personnel-list::-webkit-scrollbar {
  width: 6px;
}

.department-list::-webkit-scrollbar-thumb,
.personnel-list::-webkit-scrollbar-thumb {
  background: #dadada;
  border-radius: 4px;
}

.layui-layer {
  border: 1px solid rgba(242, 242, 242, 1);
  border-radius: 12px;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, .1);
}

/* 常用联系人样式 */
.frequent-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.frequent-header {
  background-color: #fff;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.frequent-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.frequent-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 30px;
}

.frequent-category {
  margin-bottom: 30px;
}

.category-header {
  font-size: 16px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 15px;
  padding-left: 5px;
}

.category-contacts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}


.contact-info {
  flex: 1;
}

.contact-top {
  display: flex;
  align-items: center;
  gap: 16px;
}



.contact-department {
  font-size: 12px;
  color: #909399;
}

.frequent-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: #909399;
}

.frequent-empty-state .empty-icon {
  margin-bottom: 20px;
}

.frequent-empty-state .empty-icon i {
  font-size: 80px;
  color: #c0c4cc;
  display: block;
}

.frequent-empty-state .empty-text {
  font-size: 18px;
  color: #606266;
  font-weight: 400;
}

/* 我的分组样式 */
.mygroup-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mygroup-header {
  background-color: #fff;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.mygroup-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}



.mygroup-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #909399;
}

.mygroup-empty-state .empty-icon {
  margin-bottom: 20px;
}

.mygroup-empty-state .empty-icon i {
  font-size: 80px;
  color: #c0c4cc;
  display: block;
}

.mygroup-empty-state .empty-text {
  font-size: 18px;
  color: #606266;
  font-weight: 400;
}

/* 分组概览样式 */
.mygroup-overview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.group-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
}

.group-name {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
}

/* 分组详情样式 */
.mygroup-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mygroup-detail-header {
  background-color: #fff;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  font-size: 20px;
}

.nav-item {
  color: #606266;
  cursor: pointer;
  transition: color 0.3s;
}

.nav-item:hover {
  color: var(--color-primary);
}

.nav-item.current {
  color: #303133;
  /* font-weight: 600; */
  cursor: default;
}

.nav-item.current:hover {
  color: #303133;
}

.nav-separator {
  margin: 0 8px;
  color: #c0c4cc;
}

.member-count {
  font-size: 13px;
  color: #b0b3b8;
  margin-left: 4px;
}




.member-info {
  flex: 1;
}

.member-top {
  display: flex;
  align-items: center;
  gap: 16px;
}



.member-department {
  font-size: 12px;
  color: #909399;
}