* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f2f2f2;
  height: 100vh;
  font-family: "Microsoft YaHei",
    "Helvetica Neue",
    Helvetica,
    Arial,
    sans-serif;
}

body::before {
  content: "L O G O";
  position: absolute;
  top: 35%;
  left: 15%;
  font-size: 56px;
  color: #aaaaaa;
  border-radius: 8px;
  padding: 40px;
  overflow: hidden;
  font-weight: bold;
}

.forget-password-container {
  background-color: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  width: 440px;
  height: 510px;
  padding: 48px 42px 40px 40px;
  overflow: hidden;
  position: absolute;
  border-radius: 12px;
  top: 17.5%;
  right: 12.5%;
}

/* 返回登录按钮 */
.back-to-login {
  display: inline-flex;
  align-items: center;
  color: #666;
  font-size: 16px;
  text-decoration: none;
  margin-bottom: 20px;
}

.back-to-login:hover {
  color: #1890ff;
}

.back-to-login .layui-icon {
  margin-right: 5px;
  font-size: inherit;
}

/* 步骤指示器样式 */
.step-indicator {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin: 20px 0 32px 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.step-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step-circle.active {
  background-color: #1890ff;
  color: #fff;
}

.step-circle.completed {
  background-color: #52c41a;
  color: #fff;
}

.step-label {
  font-size: 14px;
  color: #666;
}

.step-line {
  height: 2px;
  background-color: #e8e8e8;
  flex: 1;
  margin: 0 10px;
  position: relative;
  top: 18px;
}

.step-line.active {
  background-color: #1890ff;
}

/* 步骤内容区域 */
.step-content {
  position: relative;
  min-height: 300px;
}

.step-pane {
  display: none;
  animation: fadeIn 0.3s;
}

.step-pane.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单样式 */
.layui-form-item {
  margin-bottom: 28px;
}

.layui-input-wrap .layui-icon {
  color: #5f5f5f;
}

.layui-input-wrap {
  position: relative;
}

.layui-input-wrap,
.layui-input-affix {
  line-height: 42px;
}

.layui-input {
  height: 42px;
  line-height: 42px;

}

/* 按钮样式 */
.layui-btn {
  width: 100%;
  height: 42px;
  background-color: #1890ff;
  border-radius: 3px;
  font-size: 16px;
}

#smsBtn {
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
  height: 42px;
  border-radius: 0 3px 3px 0;
  font-size: 14px;
  background-color: transparent;
  color: #1890ff;
  border-left: 1px solid #e6e6e6;
}

/* 手机号显示样式 */
.phone-display {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 3px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
}

.phone-display .layui-icon {
  margin-right: 10px;
  color: #1890ff;
}

.phone-display span {
  font-size: 16px;
  color: #333;
}

/* 密码规则说明 */
.password-rules {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  margin-bottom: 25px;
}

/* 成功消息样式 */
.success-message {
  text-align: center;
  padding-bottom: 20px;
}

.success-message .layui-icon {
  font-size: 60px;
  color: #52c41a;
  margin-bottom: 24px;
}

.success-message h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.success-message p {
  font-size: 14px;
  color: #888;
  margin-bottom: 30px;
}

.success-message .layui-btn {
  max-width: 200px;
  margin: 0 auto;
}

/* 响应式调整 */
/* 偏小 */
@media screen and (max-width: 1200px) {
  .forget-password-container {
    width: 400px;
    height: 480px;
  }
}

/* 偏大 */
@media screen and (min-width: 1450px) {
  .forget-password-container {
    width: 480px;
    height: 550px;
  }
}