* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f2f2f2;
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
}

.home-container {
  margin: 24px;
  padding: 0 20px;
}

.section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #2d8cf0;
  border-radius: 2px;
}

.more-link {
  color: #2d8cf0;
  font-size: 14px;
  text-decoration: none;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.module-item {
  background-color: #fff;
  border-radius: 6px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  cursor: pointer;
  position: relative;
  border: 1px solid #e9eefd;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.module-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.module-icon {
  width: 50px;
  height: 50px;
  background-color: #ecf5ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #2d8cf0;
  font-size: 24px;
}

.module-icon .layui-icon {
  font-size: 28px;
}


.module-icon.blue {
  background-color: #ecf5ff;
  color: #2d8cf0;
}

.module-icon.green {
  background-color: #f0f9eb;
  color: #67c23a;
}

.module-icon.orange {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.module-icon.purple {
  background-color: #f5f0fa;
  color: #9254de;
}

.module-title {
  font-size: 16px;
  font-weight: 400;
  color: #333;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 12px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}