:root {
  --primary-color: #4470F5;
  --bg-color: #f8f9fa;
  --card-bg: #fff;
  --text-color: #333;
  --border-color: #e5e5e5;
  --hover-bg: #f8f9fc;
}

* {
  box-sizing: border-box;
}

body {
  background-color: var(--bg-color);
  font-family: "Microsoft YaHei", Arial, sans-serif;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  height: calc(100vh - 40px);
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.grid-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.grid-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 轮播图指示器 */
.layui-carousel-ind {
  text-align: right;
  right: 16px;
  top: -38px;
  z-index: 999;
}

.layui-carousel-ind ul {
  background-color: transparent;
}

.layui-carousel-ind ul li {
  background-color: rgba(0, 0, 0, 0.1);
}

.layui-carousel-ind ul li.layui-this {
  background-color: #fff;
  height: 10px !important;
}

.news-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.news-item {
  padding: 12px 20px;
  /* border-bottom: 1px solid #f0f0f0; */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: background-color 0.2s ease;
}

.news-item:hover {
  background-color: var(--hover-bg);
}

.news-item:last-child {
  border-bottom: none;
}

.news-title {
  color: var(--text-color);
  text-decoration: none;
  flex: 1;
  margin-right: 15px;
  line-height: 1.6;
  font-size: 14px;
  position: relative;
  padding-left: 12px;
  transition: color 0.2s ease;
}

.news-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--primary-color);
  opacity: 0.7;
}

.news-title:hover {
  color: var(--primary-color);
}

.news-date {
  color: #999;
  font-size: 12px;
  white-space: nowrap;
}

.more-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.more-link::after {
  content: ">";
  display: inline-block;
  margin-left: 3px;
  transition: transform 0.2s ease;
}

.more-link:hover {
  opacity: 0.8;
}

.more-link:hover::after {
  transform: translateX(3px);
}

.activity-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 20px;
  /* border-bottom: 1px solid #f0f0f0; */
  font-size: 14px;
  transition: background-color 0.2s ease;
  border-radius: 4px;
}

.activity-item:hover {
  background-color: var(--hover-bg);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-name {
  font-weight: 500;
  color: var(--text-color);
  min-width: 80px;
  position: relative;
  padding-left: 12px;
}

.activity-name::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.activity-desc {
  color: #666;
  font-size: 14px;
  flex: 1;
  margin-left: 20px;
  line-height: 1.5;
}

/* 轮播图样式 */
.carousel-container {
  position: relative;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.layui-carousel {
  margin: 0 !important;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  background-color: var(--card-bg);
  color: var(--text-color);
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
  position: relative;
}

.card-header:not(.nav-tabs) .header-title {
  padding-left: 16px;
  position: relative;
  font-size: 16px;
}

.card-header:not(.nav-tabs) .header-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* 选项卡样式 */
.layui-tab {
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.layui-tab-title {
  border-bottom: 1px solid #f0f0f0;
  height: 48px;
  padding: 0 16px;
  flex-shrink: 0;
}

.layui-tab-title li {
  color: var(--text-color);
  height: 40px;
  line-height: 48px;
  padding: 0 20px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  font-size: 14px;
}

.layui-tab-title .layui-this {
  color: var(--primary-color);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
}

.layui-tab-title .layui-this:after {
  display: none;
}

.layui-tab-content {
  flex: 1;
  padding: 0;
  padding-top: 8px;
  overflow-y: auto;
}

.layui-tab-item {
  height: 100%;
}

/* 轮播图内容样式 */
.carousel-container .layui-carousel,
.carousel-container .layui-this {
  height: 100% !important;
}

.carousel-title-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
  padding: 15px 25px;
  z-index: 100;
}

.carousel-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20px;
}

.carousel-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.carousel-indicator-space {
  width: 120px;
}

/* layui tab 自定义样式 */
.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--primary-color);
}

.layui-tab-brief>.layui-tab-title .layui-this:after {
  border-bottom-color: var(--primary-color);
}

/* 卡片内容区域 */
.card-body {
  flex: 1;
  overflow-y: auto;
  background-color: var(--card-bg);
  padding: 0;
}

.card-body-with-padding {
  padding: 20px;
}


/* 美化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-container {
    max-width: 100%;
    padding: 0 10px;
  }
}

@media (max-width: 768px) {
  .main-container {
    grid-template-columns: 1fr;
    gap: 15px;
    height: auto;
    min-height: calc(100vh - 40px);
  }

  body {
    padding: 15px;
  }
}

