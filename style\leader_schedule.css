* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局样式变量 */
:root {
  --main-color: #0c64eb;
}

body {
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */

}

/* 公供滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.required-mark {
  color: red;
}

.schedule-container {
  display: flex;
  height: calc(100vh - 52px);
  /* 减去顶部tab的高度 */
}

/* 左侧日程列表 */
.schedule-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
  background: white;
  position: relative;
}



.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  background: #fff;
}

.schedule-item {
  margin: 12px 16px;
  padding: 12px 15px;
  background: #f4f6ff;
  border: none;
  border-left: 3px solid var(--main-color);
  border-radius: 0;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  position: relative;
  box-shadow: none;
  transition: all 0.2s;
}

.schedule-item:hover {
  opacity: .9;
  transform: translateX(4px);
}

.schedule-item.today {
  background: #f0f7ff;
}

.schedule-item.completed {
  opacity: 0.6;
  background: #f8f9fa;
  border-left-color: #ccc;
}

.schedule-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: block;
}

.schedule-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 0;
  display: block;
}

.schedule-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: white;
  margin-top: 5px;
}

.schedule-more {
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.3s;
}

.schedule-more:hover {
  background: #f0f0f0;
  color: #666;
}

.add-schedule-btn {
  width: 28px;
  height: 28px;
  background: transparent;
  color: var(--main-color);
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.add-schedule-btn .layui-icon {
  font-size: 24px;
}

.add-schedule-btn:hover {
  transform: scale(1.1);
}

.type-meeting {
  background: var(--main-color);
}

.type-activity {
  background: #52c41a;
}

.type-personal {
  background: #faad14;
}

.type-important {
  background: #f5222d;
}

/* 右侧日历区域 */
.calendar-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  overflow: hidden;
}

.calendar-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.calendar-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
}

.calendar-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

#currentMonth {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  width: 150px;
  margin: 0 15px;
  padding-right: 10px;
  text-align: center;
  cursor: pointer;
}

/* 下拉菜单样式 */
.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 140px;
  z-index: 1000;
  display: none;
  margin-top: 4px;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.dropdown-item:hover {
  background: #f8f9fa;
}

.dropdown-item:first-child {
  border-radius: 4px 4px 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 4px 4px;
}

/* 左侧标题栏样式 */
.sidebar-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 顶部Tab页签样式 */
.top-tabs-container {
  height: 52px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.top-tabs {
  display: flex;
  align-items: center;
}

.top-tabs .tab-item {
  padding: 15px 20px;
  cursor: pointer;
  color: #666;
  font-size: 16px;
  transition: all 0.3s;
  border-bottom: 2.5px solid transparent;
  margin-right: 32px;
}

.top-tabs .tab-item:hover {
  color: var(--main-color);
}

.top-tabs .tab-item.active {
  color: var(--main-color);
  background: transparent;
  font-weight: 600;
  border-bottom-color: var(--main-color);
}

.calendar-body {
  flex: 1;
  /* padding: 20px; */
  overflow: hidden;
}

#calendar {
  height: 100%;
}

/* FullCalendar 自定义样式 */
.fc-theme-standard .fc-scrollgrid {
  border: 1px solid #e6e6e6;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border-color: #f0f0f0;
}

.fc-daygrid-day:hover {
  background: #f8f9fa;
}

/* 固定日期单元格高度 */
.fc-daygrid-day {
  height: 140px !important;
  min-height: 140px !important;
  max-height: 140px !important;
  overflow: hidden;
}

/* 日程日历单元格框 */
.fc-daygrid-day-frame {
  height: 100% !important;
  min-height: 100% !important;
  max-height: 100% !important;
  padding: 0 4px;
}

.fc-daygrid-day-events {
  /* max-height: 80px !important; */
  overflow: hidden;
}

/* 日历中日程事件 */
.fc-event {
  border: none !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
  font-size: 13.5px !important;
  cursor: pointer !important;
  background-color: transparent !important;
}

.fc-event:hover {
  background-color: #efefef !important;
}

.fc-event .fc-event-main {
  color: #333;
}

/* .fc-event-meeting {
  background: var(--main-color) !important;
  color: white !important;
}

.fc-event-activity {
  background: #52c41a !important;
  color: white !important;
}

.fc-event-personal {
  background: #faad14 !important;
  color: white !important;
}

.fc-event-important {
  background: #f5222d !important;
  color: white !important;
} */
/* 标题容器 */
.fc-event .fc-event-main .fc-event-title-container {
  display: flex;
  align-items: center;
}

.fc-event .fc-event-main .fc-event-title-container::before {
  content: '';
  flex-shrink: 0;
  display: inline-block;
  width: 8px;
  height: 8px;
  transform: translateY(1px);
  margin-right: 6px;
  border-radius: 50%;
  background-color: var(--main-color) !important;
}

/* 日历事件标题段样式 */
.fc-h-event .fc-event-title {
  text-overflow: ellipsis;
}



/* 日历外表格 */
.fc-theme-standard .fc-scrollgrid {
  border-left: none;
}


/* 日历今天样式 */
.fc .fc-daygrid-day.fc-day-today {
  background-color: #fbfbfb;
}

.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-top::after {
  content: "今";
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  font-size: 12px;
  margin-left: 8px;
  background-color: #ffb800;
  color: white;
  border-radius: 50%;
}

/* 日历顶部天数样式 */
.fc .fc-daygrid-day-top {
  flex-direction: row;
  padding: 8px 6px 6px 6px;
}

/* 日历更多标签样式 */
.fc .fc-daygrid-more-link {
  padding: 0 2px;
}

/* 日历表头 */
.fc-daygrid .fc-scroller-harness {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.06);
}

.fc-daygrid .fc-col-header .fc-col-header-cell {
  padding: 8px 0;
  font-weight: normal;
}

/* 浮动新建按钮 */
.fab-button {
  position: fixed;
  bottom: 96px;
  right: 140px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--main-color);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transition: all 0.3s;
  z-index: 1000;
}

.fab-button:hover {
  background: #40a9ff;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.5);
}

.fab-button .layui-icon {
  font-size: 24px;
}

/* 弹窗样式 */
.modal-container {
  background: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  background-color: var(--main-color);
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
  text-align: center;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  color: #fff;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.modal-form-container {
  padding: 25px;
}

.modal-table {
  margin: 0;
}

.modal-table td {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
}

.label-cell {
  width: 120px;
  text-align: right;
  background: #fafafa;
  font-weight: 500;
}

.content-cell {
  font-size: 15px;
  color: #333;
}

.content-cell.highlight {
  font-weight: 600;
}

.input-cell {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
}

.textarea-cell {
  vertical-align: top;
}

.required-mark {
  color: #ff4d4f;
}

/* 按钮区域样式 */
.modal-buttons {
  padding: 20px 25px;
  text-align: right;
  background: #fafafa;
  border-top: 1px solid #e6e6e6;
  flex-shrink: 0;
}

.modal-btn {
  margin-left: 10px;
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.modal-btn-primary {
  background: var(--main-color);
  color: white;
}

.modal-btn-primary:hover {
  background: #0958d9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(12, 100, 235, 0.3);
}

.modal-btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.modal-btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.modal-btn-default {
  background: #ffff;
  color: #666;
  border: 1px solid #d9d9d9;
}

.modal-btn-default:hover {
  background: #e6e6e6;
  border-color: #bfbfbf;
}

/* 状态和类型标签样式 */
.status-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.status-tag.pending {
  background: #faad14;
}

.status-tag.confirmed {
  background: #52c41a;
}

.status-tag.cancelled {
  background: #ff4d4f;
}

.status-tag.completed {
  background: #8c8c8c;
}

.type-tag {
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 11px;
  color: white;
  margin-right: 8px;
}

.type-tag.meeting {
  background-color: var(--main-color);
}

.type-tag.activity {
  background: #52c41a;
}

.type-tag.personal {
  background: #faad14;
}

.type-tag.important {
  background: #f5222d;
}

/* 表单特殊样式 */
.radio-group {
  margin-bottom: 10px;
}

.textarea-with-margin {
  margin-top: 10px;
}

#leaderSelect+.layui-unselect {
  width: 120px;
}

/* ---------layui共有样式------------------- */
/* layui的按钮 */
.layui-btn-primary:hover {
  border-color: var(--main-color);
  color: #fff;
  background-color: var(--main-color);
}

/* 输入框聚焦 */
.layui-input:focus,
.layui-textarea:focus {
  border-color: var(--main-color) !important;
}

/* 选择下拉选中 */
.layui-form-select dl dd.layui-this {
  color: var(--main-color) !important;
}

/* 多选选中 */
.layui-form-checkbox[lay-skin=primary]:hover>i {
  border-color: var(--main-color) !important;
}

.layui-form-checked[lay-skin=primary]>i {
  background-color: var(--main-color) !important;
  border-color: var(--main-color) !important;
}

/* 单选选中 */
.layui-form-radio:hover>*,
.layui-form-radioed,
.layui-form-radioed>i {
  color: var(--main-color) !important;
}

/* 日期下拉框 */
.layui-laydate-header i:hover,
.layui-laydate-header span:hover,
.layui-laydate-footer span:hover {
  color: var(--main-color) !important;
}

.layui-laydate .layui-this,
.layui-laydate .layui-this>div {
  background-color: var(--main-color) !important;
}

#prevMonth,
#nextMonth {
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

#prevMonth:hover,
#nextMonth:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 响应式设计 */
@media (min-width: 1600px) {
  .fc-event {
    font-size: 14.5px !important;
  }

  .fc-event .fc-event-main .fc-event-title-container::before {
    width: 10px;
    height: 10px;
  }
}