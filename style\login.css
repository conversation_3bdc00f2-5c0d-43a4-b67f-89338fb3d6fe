* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f2f2f2;
  height: 100vh;
  font-family: "Microsoft YaHei",
    "Helvetica Neue",
    Helvetica,
    Arial,
    sans-serif;
}

body::before {
  content: "L O G O";
  position: absolute;
  top: 35%;
  left: 15%;
  font-size: 56px;
  color: #aaaaaa;
  border-radius: 8px;
  padding: 40px;
  overflow: hidden;
  font-weight: bold;
}

.login-container {
  background-color: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  width: 440px;
  height: 510px;
  padding: 48px 44px 40px 44px;
  overflow: hidden;
  position: absolute;
  border-radius: 12px;
  top: 17.5%;
  right: 12.5%;
}

.login-logo {
  font-size: 100px;
  color: rgba(0, 0, 0, 0.1);
  font-weight: bold;
  letter-spacing: 2px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #555;
  font-size: 22px;
  font-weight: 600;
  letter-spacing: 1px;
}

/* .login-form {
  margin-top: 10px;
}

*/
.login-tab {
  margin-bottom: 30px;
}

.login-tab .layui-tab-title {
  border-bottom-color: #e6e6e6;
  display: flex;
  justify-content: center;
  gap: 56px;

}

.login-tab .layui-tab-title li {
  font-size: 16px;
  padding: 0 25px;
  flex-basis: 50%;
  color: #666;
}

.login-tab .layui-tab-title .layui-this {
  color: #2d8cf0;
  font-weight: 500;
}

.login-tab .layui-tab-title .layui-this:after {
  border: none;
  border-bottom: 2px solid #2d8cf0;
}

.login-tab .layui-tab-item {
  margin-top: 15px;
}


.login-input {
  height: 42px;
  line-height: 42px;
  padding-left: 40px;
  border: 1px solid #e6e6e6;
  background-color: #f9f9f9;
  border-radius: 3px;
  transition: all 0.3s;
  box-sizing: border-box;
  color: #333;
}

.layui-input-wrap .layui-icon {
  color: #5f5f5f;
}

.layui-input-wrap,
.layui-input-affix {
  line-height: 42px;
}

.layui-form-item {
  margin-bottom: 24px;
}

.login-input:focus {
  border-color: #2d8cf0;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.1);
}

.login-input:hover {
  border-color: #d2d2d2;
}

.login-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  margin-top: 32px;
  background-color: #2d8cf0;
  font-size: 16px;
  border-radius: 3px;
  letter-spacing: 1px;
  font-weight: 400;
  box-shadow: 0 2px 6px rgba(45, 140, 240, 0.2);
  transition: all 0.3s;
}

.login-btn:hover {
  background-color: #2b85e4;
  box-shadow: 0 4px 12px rgba(45, 140, 240, 0.3);
}

.login-tips {
  margin-top: 15px;
  text-align: right;
}

.login-tips a {
  color: #2d8cf0;
  font-size: 14px;
  transition: all 0.3s;
}

.login-tips a:hover {
  color: #2b85e4;
  text-decoration: underline;
}

.qr-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  font-size: 20px;
  color: #bbb;
  transition: all 0.3s;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
}

.qr-toggle:hover {
  color: #2d8cf0;
  background-color: #f5f7fa;
}

.qr-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.qr-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 320px;
  height: 380px;
  background-color: #fff;
  border-radius: 6px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.qr-placeholder {
  margin: 25px auto;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.qr-title {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  font-weight: 500;
}

.qr-tips p {
  color: #999;
  font-size: 14px;
  line-height: 1.6;
}

.qr-close {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
  font-size: 18px;
  color: #bbb;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
}

.qr-close:hover {
  color: #ff5722;
  background-color: #f9f9f9;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 13px;
  color: #bbb;
  cursor: pointer;
  transition: all 0.3s;
}

.password-toggle:hover {
  color: #2d8cf0;
}

.login-footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #999;
  line-height: 1.8;
}

/* 响应式调整 */
/* 偏小 */
@media screen and (max-width: 1200px) {
  .login-container {
    width: 400px;
    height: 480px;
  }

  .login-header h2 {
    font-size: 21px;
  }

  .login-tab .layui-tab-title li {
    font-size: 15px;
  }

}

/* 偏大 */
@media screen and (min-width: 1450px) {
  .login-container {
    width: 480px;
    height: 550px;
  }

  .login-header h2 {
    font-size: 23px;
  }

  .login-tab .layui-tab-title li {
    font-size: 17px;
  }
}