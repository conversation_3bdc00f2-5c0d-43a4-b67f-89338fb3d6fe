* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
}

/* 公供滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ---------layui共有样式------------------- */
/* 全局样式变量 */
:root {
  --main-color: #0c64eb;
}

/* layui的按钮 */
.layui-btn-primary:hover {
  border-color: var(--main-color);
  color: #fff;
  background-color: var(--main-color);
}

/* 输入框聚焦 */
.layui-input:focus,
.layui-textarea:focus {
  border-color: var(--main-color) !important;
}

/* 选择下拉选中 */
.layui-form-select dl dd.layui-this {
  color: var(--main-color) !important;
}

/* 多选选中 */
.layui-form-checkbox[lay-skin=primary]:hover>i {
  border-color: var(--main-color) !important;
}

.layui-form-checked[lay-skin=primary]>i {
  background-color: var(--main-color) !important;
  border-color: var(--main-color) !important;
}

/* 单选选中 */
.layui-form-radio:hover>*,
.layui-form-radioed,
.layui-form-radioed>i {
  color: var(--main-color) !important;
}

/* 日期下拉框 */
.layui-laydate-header i:hover,
.layui-laydate-header span:hover,
.layui-laydate-footer span:hover {
  color: var(--main-color) !important;
}

.layui-laydate .layui-this,
.layui-laydate .layui-this>div {
  background-color: var(--main-color) !important;
}

/* 下拉禁用 */
.layui-select-disabled .layui-disabled {
  background-color: #fafafa;
  color: #252525 !important;
}

.meeting-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100vh;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.2);
}



/* 顶部工具栏 */

.calendar-header {
  padding: 12px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 48px;
  flex: 1;
  justify-content: flex-end;
}

.schedule-count {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.current-date {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  width: 180px;
  padding-right: 10px;
  text-align: center;
  cursor: pointer;
}

.status-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.status-applying {
  background-color: #ffa940;
}

.status-occupied {
  background-color: #1890ff;
}

.status-mine {
  background-color: #8c8c8c;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.new-booking-btn {
  background: var(--main-color);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
  letter-spacing: 0.5px;
}

.new-booking-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.new-booking-btn:active {
  transform: translateY(0);
}

/* 主体布局 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧会议室列表 */
.meeting-rooms {
  width: 240px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
}

.rooms-header {
  min-height: 84px;
  padding: 0 15px;
  background: #fafafa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  border-right: 1px solid rgba(0, 0, 0, 0.04);
  font-weight: 600;
  font-size: 16px;
  color: #333;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
}

#roomsList {
  overflow-y: auto;
}

.room-item {
  height: 80px;
  padding: 0 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin: 0 8px;
  border-radius: 8px;
}

.room-item:hover {
  background: #fafafa;
  transform: translateX(4px);
}

.room-item.active {
  background: var(--main-color);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.room-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  letter-spacing: 0.3px;
}

.room-item.active .room-name {
  color: white;
}

.room-info {
  font-size: 12px;
  color: #666;
  opacity: 0.8;
  display: flex;
  justify-content: space-between;
}

.room-item.active .room-info {
  color: rgba(255, 255, 255, 0.9);
}

/* 右侧日历区域 */
.calendar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 日期头部 */
.date-header {
  display: flex;
  background: #fafafa;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  height: 42px;
}

.date-cell {
  flex: 1;
  padding: 8px 5px;
  text-align: center;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  font-size: 13px;
  /* font-weight: 600; */
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.3s ease;
}

.date-cell:last-child {
  border-right: none;
}

/* 时间段头部 */
.time-header {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  height: 42px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

.time-group {
  flex: 1;
  display: flex;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
}

.time-group:last-child {
  border-right: none;
}

.time-slot-header {
  flex: 1;
  padding: 0 4px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #555;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  transition: all 0.3s ease;
}

.time-slot-header:hover {
  background: rgba(0, 0, 0, 0.04);
}


.time-slot-header:last-child {
  border-right: none;
}

/* 会议室行 */
.calendar-body {
  flex: 1;
  overflow-y: auto;
}

.room-row {
  height: 80px;
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  position: relative;
  transition: background-color 0.3s;
}

.room-row.highlighted {
  background-color: #f6f8ff;
}

.day-column {
  flex: 1;
  display: flex;
  border-right: 1px solid #e6e6e6;
  position: relative;
}

.day-column:last-child {
  border-right: none;
}

.time-slot {
  flex: 1;
  border-right: 1px solid #e6e6e6;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.time-slot:last-child {
  border-right: none;
}

.time-slot:hover {
  background-color: #fafafa;
}

/* 事件样式 */
.event {
  position: absolute;
  /* 4px */
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  border-radius: 2px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.3px;
}

.event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.event.applying {
  background: linear-gradient(135deg, #ffa940 0%, #fa8c16 100%);
}

.event.occupied {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.event.mine {
  background: linear-gradient(135deg, #8c8c8c 0%, #595959 100%);
}

/* 弹窗样式 */
.modal-container {
  background: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: var(--main-color);
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
  text-align: center;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  color: #fff;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.modal-form-container {
  padding: 25px;
}

.modal-table {
  margin: 0;
}

.modal-table td {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
}

.modal-table .label-cell {
  width: 120px;
  text-align: right;
  background: #fafafa;
  font-weight: 500;
}

.modal-table .content-cell {
  font-size: 15px;
  color: #333;
}

.modal-table .content-cell.highlight {
  font-weight: 600;
}

.modal-table .content-cell.vertical-top {
  vertical-align: top;
}

/* 按钮区域样式 */
.modal-buttons {
  padding: 20px 25px;
  text-align: right;
  background: #fafafa;
  border-top: 1px solid #e6e6e6;
  flex-shrink: 0;
}

.modal-btn {
  margin-left: 10px;
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.modal-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modal-btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.modal-btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.modal-btn-default {
  background: #fff;
  color: #333;
  border: 1px solid #d9d9d9;
}

.modal-btn-default:hover {
  background: #e6e6e6;
  border-color: #bfbfbf;
}

/* 表单样式 */
.form-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.form-label {
  width: 120px;
  text-align: right;
  padding-right: 15px;
  font-weight: 500;
  color: #333;
  flex-shrink: 0;
}

.form-control {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-control-half {
  width: calc(50% - 10px);
  margin-right: 20px;
}

.form-control-half:last-child {
  margin-right: 0;
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-checkbox {
  margin-right: 8px;
}

.form-checkbox-label {
  font-weight: normal;
  cursor: pointer;
}

.required {
  color: #ff4d4f;
  margin-right: 4px;
}

/* 预定弹窗特有样式 */
.booking-tips {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #fafafa;
  line-height: 1.4;
  text-align: right;
}

.booking-tips div {
  margin-bottom: 2px;
}

.booking-tips div:last-child {
  margin-bottom: 0;
}

.required-mark {
  color: red;
}

.input-cell {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
}

.textarea-cell {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
  vertical-align: top;
}

.upload-cell {
  padding: 12px 15px;
  border: 1px solid #e6e6e6;
}

.upload-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-btn {
  background: #f0f0f0;
  border: 1px solid #d9d9d9;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  background: #e6e6e6;
  border-color: #bfbfbf;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

.file-input {
  display: none;
}

/* 底部按钮区域 */
.booking-buttons {
  background: white;
  padding: 20px 30px;
  border-top: 1px solid #e6e6e6;
  text-align: center;
  flex-shrink: 0;
}

.cancel-btn {
  background: #fff;
  color: #666;
  border: 1px solid #d9d9d9;
  padding: 10px 30px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #bfbfbf;
}

.submit-btn {
  background: var(--main-color);
  color: white;
  border: none;
  padding: 10px 30px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #0958d9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(12, 100, 235, 0.3);
}

/* 上传表格 */
.upload-table th,
.upload-table td {
  font-size: 13px;
}